{"name": "giu-pay-connect", "version": "1.0.0", "main": "dist/server.js", "scripts": {"dev": "ts-node-dev --respawn --transpile-only src/server.ts", "start": "node dist/server.js", "build": "tsc", "test:email": "ts-node src/scripts/testEmail.ts", "test:welcome-flow": "ts-node src/scripts/testWelcomeEmailFlow.ts", "test:single-session": "ts-node src/scripts/testSingleSessionPolicy.ts", "test:user-messages": "node test-user-friendly-messages.js", "test:ach": "ts-node src/scripts/testACHTransfer.ts", "test:add-money": "ts-node src/scripts/testAddMoney.ts"}, "dependencies": {"@types/nodemailer": "^6.4.17", "cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.18.2", "helmet": "^7.0.0", "jsonwebtoken": "^9.0.2", "morgan": "^1.10.0", "mysql2": "^3.14.1", "nodemailer": "^7.0.4", "plaid": "^36.0.0", "winston": "^3.17.0", "zod": "^3.22.4"}, "devDependencies": {"@types/bcrypt": "^5.0.2", "@types/cors": "^2.8.19", "@types/express": "^5.0.3", "@types/jsonwebtoken": "^9.0.10", "@types/morgan": "^1.9.10", "@types/node": "^24.0.8", "@types/winston": "^2.4.4", "bcrypt": "^6.0.0", "ts-node": "^10.9.1", "ts-node-dev": "^2.0.0", "typescript": "^5.2.2"}}