<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Transaction Notification - Pay Connect</title>
  <style>
    body { 
      font-family: Arial, sans-serif; 
      line-height: 1.6; 
      color: #333; 
      margin: 0; 
      padding: 0; 
      background-color: #f4f4f4; 
    }
    .container { 
      max-width: 600px; 
      margin: 0 auto; 
      padding: 20px; 
      background-color: #ffffff; 
    }
    .header { 
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); 
      color: white; 
      padding: 30px; 
      text-align: center; 
      border-radius: 10px 10px 0 0; 
    }
    .header h1 {
      margin: 0;
      font-size: 28px;
      font-weight: bold;
    }
    .header p {
      margin: 10px 0 0 0;
      font-size: 16px;
      opacity: 0.9;
    }
    .content { 
      background: #f9f9f9; 
      padding: 30px; 
      border-radius: 0 0 10px 10px; 
    }
    .content h2 {
      color: #333;
      margin-top: 0;
    }
    .transaction-details {
      background: #fff;
      padding: 25px;
      border-radius: 8px;
      margin: 20px 0;
      border-left: 4px solid #28a745;
    }
    .transaction-details.debit {
      border-left-color: #dc3545;
    }
    .detail-row {
      display: flex;
      justify-content: space-between;
      margin: 10px 0;
      padding: 8px 0;
      border-bottom: 1px solid #eee;
    }
    .detail-row:last-child {
      border-bottom: none;
      font-weight: bold;
      font-size: 18px;
    }
    .detail-label {
      font-weight: bold;
      color: #666;
    }
    .detail-value {
      color: #333;
    }
    .amount {
      font-size: 24px;
      font-weight: bold;
    }
    .amount.credit {
      color: #28a745;
    }
    .amount.debit {
      color: #dc3545;
    }
    .footer { 
      text-align: center; 
      margin-top: 30px; 
      color: #666; 
      font-size: 14px; 
    }
    .footer p {
      margin: 5px 0;
    }
    .footer small {
      color: #999;
    }
    @media only screen and (max-width: 600px) {
      .container {
        padding: 10px;
      }
      .header, .content {
        padding: 20px;
      }
      .detail-row {
        flex-direction: column;
        gap: 5px;
      }
    }
  </style>
</head>
<body>
  <div class="container">
    <div class="header">
      <h1>💳 Pay Connect</h1>
      <p>Transaction Notification</p>
    </div>
    <div class="content">
      <h2>Transaction {{TRANSACTION_TYPE}}</h2>
      
      <div class="transaction-details {{TRANSACTION_CLASS}}">
        <div class="detail-row">
          <span class="detail-label">Transaction ID:</span>
          <span class="detail-value">{{TRANSACTION_ID}}</span>
        </div>
        <div class="detail-row">
          <span class="detail-label">Date & Time:</span>
          <span class="detail-value">{{TRANSACTION_DATE}}</span>
        </div>
        <div class="detail-row">
          <span class="detail-label">Description:</span>
          <span class="detail-value">{{TRANSACTION_DESCRIPTION}}</span>
        </div>
        <div class="detail-row">
          <span class="detail-label">{{AMOUNT_LABEL}}:</span>
          <span class="detail-value amount {{TRANSACTION_CLASS}}">{{TRANSACTION_AMOUNT}}</span>
        </div>
        <div class="detail-row">
          <span class="detail-label">Current Balance:</span>
          <span class="detail-value">{{CURRENT_BALANCE}}</span>
        </div>
      </div>
      
      <p>{{TRANSACTION_MESSAGE}}</p>
      
      <p>If you have any questions about this transaction, please contact our support team or check your transaction history in the app.</p>
      
      <div class="footer">
        <p>Best regards,<br><strong>Pay Connect Team</strong></p>
        <p><small>This is an automated transaction notification. Please do not reply to this email.</small></p>
      </div>
    </div>
  </div>
</body>
</html>
