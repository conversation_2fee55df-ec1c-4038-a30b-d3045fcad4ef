import express from 'express';
import {
  getUserStaff,
  getUserDetailsInfo,
  getUserDepartmentInfo,
  getUserManagerInfo,
  getUserTeam,
  getUserProjectsInfo,
  getUserPermissionsInfo,
  getComprehensiveUserData,
  importUsers,
  getStaffTransactions,
  addStaffBankAccount,
  getStaffBankAccounts,
  transferToStaffBank,
  getSingleStaffBankAccounts,
  updateStaffBankAccount,
  syncTransferStatus,
  getTransferStatus
} from '../controllers/staffController';
import { authenticateTokenAndSession } from '../middlewares/sessionAuth';

const router = express.Router();

// All staff routes require full authentication (JWT + session)
router.use(authenticateTokenAndSession);

// Staff and user information routes (using secondary database)
router.get('/staff', getUserStaff);                    // Get user's staff members
router.get('/details', getUserDetailsInfo);            // Get user details
router.get('/department', getUserDepartmentInfo);      // Get user's department
router.get('/manager', getUserManagerInfo);            // Get user's manager
router.get('/team', getUserTeam);                      // Get user's team members
router.get('/projects', getUserProjectsInfo);          // Get user's projects
router.get('/permissions', getUserPermissionsInfo);    // Get user's permissions
router.get('/comprehensive', getComprehensiveUserData); // Get all user info

// Staff management routes
router.post('/import-users', importUsers);             // Import multiple users as staff

// Transaction routes
router.get('/transactions', getStaffTransactions);     // Get staff member transactions with pagination


// Staff bank account routes
router.post('/bank-account', addStaffBankAccount);        // Add staff bank account
router.get('/bank-accounts', getStaffBankAccounts);       // Get all staff bank accounts
router.get('/bank-accounts/:staffId', getSingleStaffBankAccounts); // Get specific staff bank account
router.put('/bank-accounts/:accountId', updateStaffBankAccount); // Update staff bank account
router.post('/transfer-to-bank', transferToStaffBank);    // Transfer to staff bank account

// Transfer status routes
router.post('/sync-transfer-status', syncTransferStatus); // Sync transfer statuses with Plaid
router.get('/transfer-status/:transferId', getTransferStatus); // Get individual transfer status

export default router;
