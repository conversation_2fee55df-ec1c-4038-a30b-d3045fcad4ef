import express from 'express';
import {
  markTransferAsSuccessful,
  markTransferAsFailed,
  markTransferAsReturned,
  cancelTransfer,
  simulateTransfer,
  getTransferStatus,
  getAvailableFailureReasons,
  batchSimulateTransfers
} from '../controllers/plaidSandboxController';
import { authenticateToken } from '../middlewares/auth';

const router = express.Router();

// Apply authentication middleware to all routes
router.use(authenticateToken);

// Middleware to ensure sandbox environment only
const ensureSandboxEnvironment = (_req: any, res: any, next: any) => {
  if (process.env.PLAID_ENV !== 'sandbox') {
    return res.status(403).json({
      success: false,
      message: 'Sandbox testing endpoints are only available in sandbox environment'
    });
  }
  next();
};

// Apply sandbox check to all routes
router.use(ensureSandboxEnvironment);

/**
 * @route POST /api/plaid-sandbox/transfer/success
 * @desc Mark a transfer as successful (sandbox only)
 * @access Private
 * @body { transferId: string, delay?: number }
 */
router.post('/transfer/success', markTransferAsSuccessful);

/**
 * @route POST /api/plaid-sandbox/transfer/fail
 * @desc Mark a transfer as failed (sandbox only)
 * @access Private
 * @body { transferId: string, failureReason?: TransferFailureReason, customReason?: string, delay?: number }
 */
router.post('/transfer/fail', markTransferAsFailed);

/**
 * @route POST /api/plaid-sandbox/transfer/return
 * @desc Mark a transfer as returned (sandbox only)
 * @access Private
 * @body { transferId: string, returnReason?: string, delay?: number }
 */
router.post('/transfer/return', markTransferAsReturned);

/**
 * @route POST /api/plaid-sandbox/transfer/cancel
 * @desc Cancel a transfer (sandbox only)
 * @access Private
 * @body { transferId: string, delay?: number }
 */
router.post('/transfer/cancel', cancelTransfer);

/**
 * @route POST /api/plaid-sandbox/transfer/simulate
 * @desc Simulate a transfer with specific outcome (sandbox only)
 * @access Private
 * @body TransferSimulationOptions
 */
router.post('/transfer/simulate', simulateTransfer);

/**
 * @route GET /api/plaid-sandbox/transfer/status/:transferId
 * @desc Get transfer status from Plaid
 * @access Private
 */
router.get('/transfer/status/:transferId', getTransferStatus);

/**
 * @route GET /api/plaid-sandbox/failure-reasons
 * @desc Get available failure reasons for testing
 * @access Private
 */
router.get('/failure-reasons', getAvailableFailureReasons);

/**
 * @route POST /api/plaid-sandbox/transfer/batch-simulate
 * @desc Batch simulate multiple transfers
 * @access Private
 * @body { simulations: TransferSimulationOptions[] }
 */
router.post('/transfer/batch-simulate', batchSimulateTransfers);

export default router;
