import express from 'express';
import {
  getWalletInfo,
  createWallet,
  changeWalletPin,
  sendPinChangeOTP,
  sendPinResetOTP,
  resetWalletPin,
  verifyWalletPin,
  getBalance,
  checkPinStatus,
  addMoney,
  withdrawMoney,
  getTransactionHistory,
  getEnhancedWalletInfo,
  getDashboardData,
  getPendingTransfersController,
  transferToWallet,
  searchUsers,
  checkUserWallet,
  getWithdrawalStatus,
  getPlaidLedgerStatus,
  settlePlaidLedger
} from '../controllers/walletController';
import { authenticateTokenAndSession } from '../middlewares/sessionAuth';

const router = express.Router();

// All wallet routes require full authentication (JWT + session)
router.use(authenticateTokenAndSession);

// Wallet management routes
router.get('/info', getWalletInfo);
router.get('/enhanced-info', getEnhancedWalletInfo); // Enhanced info with bank details
router.get('/dashboard', getDashboardData); // Dashboard data with stats and recent transactions
router.post('/create', createWallet); // Now includes PIN setup
router.get('/balance', getBalance);

// Transaction management routes
router.get('/transactions', getTransactionHistory);

// PIN management routes
router.get('/pin-status', checkPinStatus);
router.post('/send-pin-change-otp', sendPinChangeOTP);
router.post('/change-pin', changeWalletPin);
router.post('/send-pin-reset-otp', sendPinResetOTP);
router.post('/reset-pin', resetWalletPin);
router.post('/verify-pin', verifyWalletPin);

// Money management routes (now with PIN verification)
router.post('/add-money', addMoney); // Requires PIN and uses primary bank
router.post('/withdraw', withdrawMoney); // Requires PIN and transfers to primary bank
router.get('/withdrawal-status/:transactionId', getWithdrawalStatus); // Get real-time withdrawal status

// Transfer status routes
router.get('/pending-transfers', getPendingTransfersController); // Get pending ACH transfers

// Wallet-to-wallet transfer routes
router.post('/transfer', transferToWallet); // Transfer money to another wallet
router.get('/search-users', searchUsers); // Search for transfer recipients
router.get('/check-user-wallet', checkUserWallet); // Check if user has wallet (debug endpoint)

// Plaid Ledger management routes (admin/debug)
router.get('/plaid-ledger/status', getPlaidLedgerStatus); // Get Plaid Ledger balance and status
router.post('/plaid-ledger/settle', settlePlaidLedger); // Settle pending funds in Plaid Ledger

export default router;
