import { Router, Request, Response, NextFunction } from 'express';
import {
  createLinkToken,
  exchangePublicToken,
  getAccounts,
  setPrimaryAccount,
  disconnectAccount,
  plaidController
} from '../controllers/plaidController';
import { handlePlaidWebhook } from '../controllers/plaidWebhookController';
import { validatePlaidWebhook } from '../middlewares/plaidWebhookValidation';
import { authenticateTokenAndSession } from '../middlewares/sessionAuth';

const router = Router();

// Create link token endpoint
router.post('/link-token', (req: Request, res: Response, next: NextFunction) => {
  createLinkToken(req, res).catch(next);
});

// Exchange public token endpoint
router.post('/exchange-public-token', authenticateTokenAndSession, (req: Request, res: Response, next: NextFunction) => {
  exchangePublicToken(req, res).catch(next);
});

// Get connected accounts endpoint
router.get('/accounts', authenticateTokenAndSession, (req: Request, res: Response, next: NextFunction) => {
  getAccounts(req, res).catch(next);
});

// Set primary account endpoint
router.patch('/accounts/:accountId/set-primary', authenticateTokenAndSession, (req: Request, res: Response, next: NextFunction) => {
  setPrimaryAccount(req, res).catch(next);
});

// Disconnect account endpoint
router.delete('/accounts/:accountId', authenticateTokenAndSession, (req: Request, res: Response, next: NextFunction) => {
  disconnectAccount(req, res).catch(next);
});

// Webhook endpoint for Plaid transfer status updates (no auth required)
router.post('/webhook', validatePlaidWebhook, (req: Request, res: Response, next: NextFunction) => {
  handlePlaidWebhook(req, res).catch(next);
});

// Legacy endpoint for backward compatibility
router.post('/exchange_public_token', authenticateTokenAndSession, (req: Request, res: Response, next: NextFunction) => {
  plaidController(req, res).catch(next);
});

export default router;
