import express from 'express';
import { login, logout, verifyOTP, resendOTP, cleanupExpiredOTPs, cleanupExpiredSessions,  addUserWithAddAccount, addUsersWithAddMember, generateToken } from '../controllers/userController';
import { authenticateTokenOnly, authenticateTokenAndSession } from '../middlewares/sessionAuth';

const router = express.Router();

// Authentication routes
router.post('/login', login);
router.post('/verify-otp', authenticateTokenOnly, verifyOTP); // Only JWT validation needed for OTP verification
router.get('/resend-otp', authenticateTokenOnly, resendOTP);  // Only JWT validation needed for OTP resend
router.post('/logout', authenticateTokenAndSession, logout);  // Full session validation for logout
router.post('/generate-token', generateToken)
// Admin/utility routes
router.delete('/cleanup-expired-otps', cleanupExpiredOTPs);
router.delete('/cleanup-expired-sessions', cleanupExpiredSessions);

// router.get('/profile', authenticateTokenAndSession, getProfile);
router.post('/addUser', addUsersWithAddMember);
router.post('/addUserWithAddAccount', addUserWithAddAccount);


export default router;