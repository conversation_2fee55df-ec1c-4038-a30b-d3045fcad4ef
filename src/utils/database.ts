import { RowDataPacket, ResultSetHeader } from 'mysql2/promise';
import pool, { primaryDb, secondaryDb } from '../config/db';
import logger from './logger';

// Types for query results
export type QueryResult<T = any> = T[];
export type QueryResultRow = RowDataPacket;
export type QueryResultHeader = ResultSetHeader;

// Interface for database query parameters
export interface QueryParams {
  query: string;
  params?: any[];
}

/**
 * Execute a SELECT query and return the results
 * @param query - SQL query string
 * @param params - Query parameters for prepared statements
 * @returns Promise<QueryResult<T>>
 */
export async function executeQuery<T = QueryResultRow>(
  query: string, 
  params: any[] = []
): Promise<QueryResult<T>> {
  let connection;
  try {
    connection = await pool.getConnection();
    logger.info(`Executing query: ${query}`, { params });
    
    const [rows] = await connection.execute<RowDataPacket[]>(query, params);
    
    logger.info(`Query executed successfully, returned ${rows.length} rows`);
    return rows as QueryResult<T>;
  } catch (error) {
    logger.error('Database query error:', { query, params, error });
    throw new Error(`Database query failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
  } finally {
    if (connection) {
      connection.release();
    }
  }
}

/**
 * Execute an INSERT, UPDATE, or DELETE query
 * @param query - SQL query string
 * @param params - Query parameters for prepared statements
 * @returns Promise<QueryResultHeader>
 */
export async function executeUpdate(
  query: string, 
  params: any[] = []
): Promise<QueryResultHeader> {
  let connection;
  try {
    connection = await pool.getConnection();
    logger.info(`Executing update query: ${query}`, { params });
    
    const [result] = await connection.execute<ResultSetHeader>(query, params);
    
    logger.info(`Update query executed successfully`, { 
      affectedRows: result.affectedRows,
      insertId: result.insertId 
    });
    return result;
  } catch (error) {
    logger.error('Database update error:', { query, params, error });
    throw new Error(`Database update failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
  } finally {
    if (connection) {
      connection.release();
    }
  }
}

/**
 * Execute a transaction with multiple queries
 * @param queries - Array of query objects with query string and parameters
 * @returns Promise<any[]> - Array of results from each query
 */
export async function executeTransaction(queries: QueryParams[]): Promise<any[]> {
  let connection;
  try {
    connection = await pool.getConnection();
    await connection.beginTransaction();
    
    logger.info(`Starting transaction with ${queries.length} queries`);
    
    const results: any[] = [];
    
    for (const { query, params = [] } of queries) {
      const [result] = await connection.execute(query, params);
      results.push(result);
    }
    
    await connection.commit();
    logger.info('Transaction committed successfully');
    
    return results;
  } catch (error) {
    if (connection) {
      await connection.rollback();
      logger.error('Transaction rolled back due to error:', error);
    }
    throw new Error(`Transaction failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
  } finally {
    if (connection) {
      connection.release();
    }
  }
}

/**
 * Execute a query and return the first row or null
 * @param query - SQL query string
 * @param params - Query parameters for prepared statements
 * @returns Promise<T | null>
 */
export async function executeQuerySingle<T = QueryResultRow>(
  query: string, 
  params: any[] = []
): Promise<T | null> {
  const results = await executeQuery<T>(query, params);
  return results.length > 0 ? results[0] : null;
}

/**
 * Check if a record exists
 * @param query - SQL query string (should return count or specific record)
 * @param params - Query parameters for prepared statements
 * @returns Promise<boolean>
 */
export async function recordExists(
  query: string,
  params: any[] = []
): Promise<boolean> {
  const result = await executeQuerySingle<{ count?: number }>(query, params);
  return result ? (result.count || 0) > 0 : false;
}

// ==================== SECONDARY DATABASE FUNCTIONS ====================

/**
 * Execute a SELECT query on secondary database and return the results
 * @param query - SQL query string
 * @param params - Query parameters for prepared statements
 * @returns Promise<QueryResult<T>>
 */
export async function executeSecondaryQuery<T = QueryResultRow>(
  query: string,
  params: any[] = []
): Promise<QueryResult<T>> {
  let connection;
  try {
    connection = await secondaryDb.getConnection();
    const [rows] = await connection.execute<RowDataPacket[]>(query, params);
    return rows as QueryResult<T>;
  } catch (error) {
    logger.error('Secondary database query error:', { query, params, error });
    throw new Error(`Secondary database query failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
  } finally {
    if (connection) {
      connection.release();
    }
  }
}

/**
 * Execute a query on secondary database and return the first row or null
 * @param query - SQL query string
 * @param params - Query parameters for prepared statements
 * @returns Promise<T | null>
 */
export async function executeSecondaryQuerySingle<T = QueryResultRow>(
  query: string,
  params: any[] = []
): Promise<T | null> {
  const results = await executeSecondaryQuery<T>(query, params);
  return results.length > 0 ? results[0] : null;
}
