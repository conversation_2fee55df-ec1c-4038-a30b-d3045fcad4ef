import { Response } from 'express';
import logger from './logger';
import { getUserMessage, getUserTip, USER_MESSAGES } from './userMessages';

// Standard response interfaces
export interface ApiResponse<T = any> {
  success: boolean;
  message: string;
  data?: T;
  error?: string;
  tip?: string;
  timestamp: string;
  statusCode: number;
}

export interface PaginatedResponse<T = any> extends ApiResponse<T> {
  pagination?: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
}

export interface ValidationError {
  field: string;
  message: string;
}

export interface ApiErrorResponse extends ApiResponse {
  errors?: ValidationError[];
  stack?: string;
}

/**
 * Send a successful response with user-friendly messaging
 * @param res - Express Response object
 * @param data - Response data
 * @param message - Success message
 * @param statusCode - HTTP status code (default: 200)
 * @param tip - Optional user tip
 */
export function sendSuccess<T>(
  res: Response,
  data?: T,
  message: string = 'Success',
  statusCode: number = 200,
  tip?: string
): void {
  const response: ApiResponse<T> = {
    success: true,
    message,
    data,
    tip,
    timestamp: new Date().toISOString(),
    statusCode
  };

  logger.info('Sending success response', { statusCode, message });
  res.status(statusCode).json(response);
}

/**
 * Send a user-friendly success response using predefined messages
 * @param res - Express Response object
 * @param category - Message category from USER_MESSAGES
 * @param action - Specific action within the category
 * @param data - Response data
 * @param statusCode - HTTP status code (default: 200)
 */
export function sendUserSuccess<T>(
  res: Response,
  category: keyof typeof USER_MESSAGES,
  action: string,
  data?: T,
  statusCode: number = 200
): void {
  const message = getUserMessage(category, action, 'success');
  const tip = getUserTip(category, action);

  sendSuccess(res, data, message, statusCode, tip);
}

/**
 * Send a paginated response
 * @param res - Express Response object
 * @param data - Response data
 * @param pagination - Pagination information
 * @param message - Success message
 * @param statusCode - HTTP status code (default: 200)
 */
export function sendPaginatedSuccess<T>(
  res: Response,
  data: T,
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  },
  message: string = 'Success',
  statusCode: number = 200
): void {
  const response: PaginatedResponse<T> = {
    success: true,
    message,
    data,
    pagination,
    timestamp: new Date().toISOString(),
    statusCode
  };

  logger.info('Sending paginated success response', { statusCode, message, pagination });
  res.status(statusCode).json(response);
}

/**
 * Send an error response with user-friendly messaging
 * @param res - Express Response object
 * @param message - Error message
 * @param statusCode - HTTP status code (default: 500)
 * @param error - Additional error details
 * @param errors - Validation errors array
 * @param tip - Optional user tip
 */
export function sendError(
  res: Response,
  message: string = 'We encountered an unexpected issue. Please try again or contact support',
  statusCode: number = 500,
  error?: string,
  errors?: ValidationError[],
  tip?: string
): void {
  const response: ApiErrorResponse = {
    success: false,
    message,
    error,
    errors,
    tip,
    timestamp: new Date().toISOString(),
    statusCode
  };

  // Add stack trace in development mode
  if (process.env.NODE_ENV === 'development' && error) {
    response.stack = error;
  }

  logger.error('Sending error response', { statusCode, message, error });
  res.status(statusCode).json(response);
}

/**
 * Send a user-friendly error response using predefined messages
 * @param res - Express Response object
 * @param category - Message category from USER_MESSAGES
 * @param action - Specific action within the category
 * @param statusCode - HTTP status code (default: 500)
 * @param error - Additional error details
 * @param errors - Validation errors array
 */
export function sendUserError(
  res: Response,
  category: keyof typeof USER_MESSAGES,
  action: string,
  statusCode: number = 500,
  error?: string,
  errors?: ValidationError[]
): void {
  const message = getUserMessage(category, action, 'error');
  const tip = getUserTip(category, action);

  sendError(res, message, statusCode, error, errors, tip);
}

/**
 * Send a validation error response
 * @param res - Express Response object
 * @param errors - Array of validation errors
 * @param message - Error message
 */
export function sendValidationError(
  res: Response,
  errors: ValidationError[],
  message: string = 'Validation failed'
): void {
  sendError(res, message, 400, undefined, errors);
}

/**
 * Send a not found response
 * @param res - Express Response object
 * @param resource - Name of the resource that was not found
 */
export function sendNotFound(
  res: Response,
  resource: string = 'Resource'
): void {
  sendError(res, `${resource} not found`, 404);
}

/**
 * Send an unauthorized response
 * @param res - Express Response object
 * @param message - Error message
 */
export function sendUnauthorized(
  res: Response,
  message: string = 'Unauthorized'
): void {
  sendError(res, message, 401);
}

/**
 * Send a forbidden response
 * @param res - Express Response object
 * @param message - Error message
 */
export function sendForbidden(
  res: Response,
  message: string = 'Forbidden'
): void {
  sendError(res, message, 403);
}

/**
 * Send a conflict response
 * @param res - Express Response object
 * @param message - Error message
 */
export function sendConflict(
  res: Response,
  message: string = 'Conflict'
): void {
  sendError(res, message, 409);
}

/**
 * Send a rate limit exceeded response
 * @param res - Express Response object
 * @param message - Error message
 */
export function sendRateLimitExceeded(
  res: Response,
  message: string = 'Too many requests'
): void {
  sendError(res, message, 429);
}

/**
 * Send a created response
 * @param res - Express Response object
 * @param data - Created resource data
 * @param message - Success message
 */
export function sendCreated<T>(
  res: Response,
  data?: T,
  message: string = 'Resource created successfully'
): void {
  sendSuccess(res, data, message, 201);
}

/**
 * Send a no content response
 * @param res - Express Response object
 */
export function sendNoContent(res: Response): void {
  logger.info('Sending no content response');
  res.status(204).send();
}

/**
 * Handle async controller errors
 * @param fn - Async controller function
 * @returns Express middleware function
 */
export function asyncHandler(fn: Function) {
  return (req: any, res: any, next: any) => {
    Promise.resolve(fn(req, res, next)).catch(next);
  };
}
