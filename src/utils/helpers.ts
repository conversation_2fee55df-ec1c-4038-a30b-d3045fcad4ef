import crypto from 'crypto';
import { sendError, sendUnauthorized } from './response';
import logger from './logger';
const textEncoder = new TextEncoder();
const textDecoder = new TextDecoder();
import { Response } from 'express';
const IV_LENGTH = 16;
const ALGORITHM = 'aes-256-gcm';

/**
 * Get or generate encryption key
 */
function getEncryptionKey() {
  if (!process.env.ENCRYPTION_SECRET) {
    console.warn("ENCRYPTION_SECRET not found, generating a new one.");
    return crypto.randomBytes(32);
  }

  // Remove hyphens and convert to hex
  const cleanSecret = process.env.ENCRYPTION_SECRET.replace(/-/g, '');

  try {
    // Try to parse as hex first
    const hexKey = Buffer.from(cleanSecret, 'hex');
    if (hexKey.length === 32) {
      return hexKey;
    }
  } catch (error) {
    // If hex parsing fails, fall through to string-based key derivation
  }

  // If not valid hex or wrong length, derive key from string using crypto.scrypt
  const salt = Buffer.from('giu-interactive-api-salt', 'utf8'); // Fixed salt for consistency
  return crypto.scryptSync(process.env.ENCRYPTION_SECRET, salt, 32);
}

/**
 * Decrypt a message encrypted with encryptMessage()
 */
export async function decryptMessage(encryptedBase64: string) {
  const key = getEncryptionKey();
  const payload = Buffer.from(encryptedBase64, 'base64');

  const iv = payload.subarray(0, IV_LENGTH);
  const encrypted = payload.subarray(IV_LENGTH, payload.length - 16);
  const authTag = payload.subarray(payload.length - 16);

  const decipher = crypto.createDecipheriv(ALGORITHM, key, iv);
  decipher.setAuthTag(authTag);

  const decrypted = decipher.update(encrypted) + decipher.final('utf8');

  // Parse the decrypted string as JSON
  try {
    return JSON.parse(decrypted);
  } catch (error) {
    throw new Error('Failed to parse decrypted message as JSON');
  }
}



export function handleApiError(res: Response, error: unknown, context: string, email?: string) {
  const errorMessage = error instanceof Error ? error.message : 'Unknown error';
  
  logger.error(`${context} error`, {
    error: errorMessage,
    email: email || 'unknown'
  });

  if (error instanceof Error && error.message.includes('decrypt')) {
    return sendUnauthorized(res, 'Invalid authentication data');
  }

  return sendError(res, `${context} failed`, 500, errorMessage);
}

 // Common type for decrypted user data
type DecryptedUserData = {
  user_id?: string;
  parent_user_id?: number;
  contact:number;
  avatar:string;
  id?: string;
  email?: string;
  firstname?: string;
  lastname?: string;
  role_id?: number;
  is_organizer?: boolean;
};

// Helper function for common validation and decryption
export async function validateAndDecryptRequest(body: string): Promise<DecryptedUserData> {
  if (!body) {
    throw new Error('Authentication data is required');
  }

  const decodedData = await decryptMessage(body);
  
  if (!decodedData || typeof decodedData !== 'object') {
    logger.warn('Decrypted data is invalid', { decodedData });
    throw new Error('Invalid authentication data format');
  }

  return decodedData;
}