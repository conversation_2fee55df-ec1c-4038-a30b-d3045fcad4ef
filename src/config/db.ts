import mysql from 'mysql2/promise';
import dotenv from 'dotenv';

dotenv.config();

// Primary database configuration
const primaryDbConfig = {
  host: process.env.DB_HOST || 'localhost',
  user: process.env.DB_USER || 'root',
  password: process.env.DB_PASSWORD || 'password',
  database: process.env.DB_DATABASE || 'payments',
  port: process.env.DB_PORT ? Number(process.env.DB_PORT) : 3306,
};

// Secondary database configuration for special use cases
const secondaryDbConfig = {
  host: process.env.DB_SECONDARY_HOST || process.env.DB_HOST || 'localhost',
  user: process.env.DB_SECONDARY_USER || process.env.DB_USER || 'root',
  password: process.env.DB_SECONDARY_PASSWORD || process.env.DB_PASSWORD || 'password',
  database: process.env.DB_SECONDARY_DATABASE || 'payments_secondary',
  port: process.env.DB_SECONDARY_PORT
    ? Number(process.env.DB_SECONDARY_PORT)
    : (process.env.DB_PORT ? Number(process.env.DB_PORT) : 3306),
};

// Primary database pool
const primaryPool = mysql.createPool({
  ...primaryDbConfig,
  connectionLimit: 10,
  queueLimit: 0,
  idleTimeout: 60000,
  enableKeepAlive: true,
});

// Secondary database pool
const secondaryPool = mysql.createPool({
  ...secondaryDbConfig,
  connectionLimit: 5, // Smaller pool for secondary database
  queueLimit: 0,
  idleTimeout: 60000,
  enableKeepAlive: true,
});

console.log('MySQL connection pools created (Primary & Secondary)');

// Primary database connection
export const getConnection = async () => {
  try {
    const connection = await primaryPool.getConnection();
    console.log('Connected to primary MySQL database');
    return connection;
  } catch (error) {
    console.error('Error connecting to primary MySQL database:', error);
    throw error;
  }
};

// Secondary database connection
export const getSecondaryConnection = async () => {
  try {
    const connection = await secondaryPool.getConnection();
    console.log('Connected to secondary MySQL database');
    return connection;
  } catch (error) {
    console.error('Error connecting to secondary MySQL database:', error);
    throw error;
  }
};

// Test database connections
export const testConnections = async () => {
  try {
    // Test primary connection
    const primaryConn = await getConnection();
    await primaryConn.ping();
    primaryConn.release();
    console.log('✅ Primary database connection successful');

    // Test secondary connection
    const secondaryConn = await getSecondaryConnection();
    await secondaryConn.ping();
    secondaryConn.release();
    console.log('✅ Secondary database connection successful');

    return { primary: true, secondary: true };
  } catch (error) {
    console.error('❌ Database connection test failed:', error);
    return { primary: false, secondary: false };
  }
};

// Get database configuration info
export const getDatabaseInfo = () => {
  return {
    primary: {
      host: primaryDbConfig.host,
      database: primaryDbConfig.database,
      user: primaryDbConfig.user,
    },
    secondary: {
      host: secondaryDbConfig.host,
      database: secondaryDbConfig.database,
      user: secondaryDbConfig.user,
    },
  };
};

// Export pools
export const primaryDb = primaryPool;
export const secondaryDb = secondaryPool;

// Default export is primary pool for backward compatibility
export default primaryPool;
