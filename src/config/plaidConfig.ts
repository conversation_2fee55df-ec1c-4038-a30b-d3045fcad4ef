import { Configuration, PlaidApi, PlaidEnvironments } from 'plaid';

export const plaidClientId = process.env.PLAID_CLIENT_ID || '68628fba1a6ea30025317ece';
export const plaidSecret = process.env.PLAID_SECRET || 'b90421bb7b3c3b405518742ffb15aa';
export const plaidEnv = process.env.PLAID_ENV || 'sandbox'; // 'sandbox' for local, 'production' for live

// Always use real Plaid transfers - only environment differs (sandbox vs production)
export const useRealTransfers = true;

// Determine Plaid environment based on configuration
const getPlaidEnvironment = () => {
  switch (plaidEnv) {
    case 'production':
      return PlaidEnvironments.production;
    case 'development':
      return PlaidEnvironments.development;
    case 'sandbox':
    default:
      return PlaidEnvironments.sandbox;
  }
};

// Plaid client configuration
const configuration = new Configuration({
  basePath: getPlaidEnvironment(),
  baseOptions: {
    headers: {
      'PLAID-CLIENT-ID': plaidClientId,
      'PLAID-SECRET': plaidSecret,
    },
  },
});

export const plaidClient = new PlaidApi(configuration);
