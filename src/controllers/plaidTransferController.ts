import { Request, Response } from 'express';
import logger from '../utils/logger';
import { 
  createACHDebitTransfer, 
  createACHCreditTransfer,
  getTransferStatus, 
  syncPendingTransfers,
  createStaffBankTransfer,
  createTransferAuthorization,
  TransferAuthorizationResult
} from '../services/plaidTransferService';
import { getUserMasterWallet, updateWalletBalance } from '../services/walletService';
import { comparePin } from '../models/user';
import { sendSuccess, sendError, sendUnauthorized } from '../utils/response';
import { TransferType, TransferNetwork, ACHClass } from 'plaid';

/**
 * Create a transfer from user's bank account to their wallet
 */
export const createTransfer = async (req: Request & { userId?: string }, res: Response) => {
  try {
    const userId = req.userId;
    if (!userId) {
      return sendUnauthorized(res, 'User not authenticated');
    }

    const { amount, description, bankAccountId, paymentMethodType } = req.body;

    if (!amount || isNaN(parseFloat(amount)) || parseFloat(amount) <= 0) {
      return sendError(res, 'Valid amount is required', 400);
    }

    const transferAmount = parseFloat(amount);
    
    const result = await createACHDebitTransfer(
      parseInt(userId), 
      transferAmount, 
      description || 'Add money to wallet', 
      bankAccountId,
      paymentMethodType || 'ach'
    );

    if (result.success) {
      return sendSuccess(res, {
        transferId: result.transferId,
        status: result.status
      }, result.message);
    } else {
      return sendError(res, result.message || 'Failed to create transfer', 400);
    }
  } catch (error) {
    logger.error('Error creating transfer', { error });
    return sendError(res, 'Failed to create transfer', 500);
  }
};

/**
 * Get transfer status
 */
export const getTransferStatusController = async (req: Request, res: Response) => {
  try {
    const { transferId } = req.params;
    
    if (!transferId) {
      return sendError(res, 'Transfer ID is required', 400);
    }

    const result = await getTransferStatus(transferId);

    if (result.success) {
      return sendSuccess(res, {
        transferId: result.transferId,
        status: result.status
      }, result.message);
    } else {
      return sendError(res, result.message || 'Failed to get transfer status', 400);
    }
  } catch (error) {
    logger.error('Error getting transfer status', { error });
    return sendError(res, 'Failed to get transfer status', 500);
  }
};

/**
 * Sync pending transfers
 */
export const syncTransfers = async (req: Request, res: Response) => {
  try {
    await syncPendingTransfers();
    return sendSuccess(res, {}, 'Pending transfers synced successfully');
  } catch (error) {
    logger.error('Error syncing transfers', { error });
    return sendError(res, 'Failed to sync transfers', 500);
  }
};

/**
 * Create a transfer to staff bank account
 */
export const createStaffTransfer = async (req: Request & { userId?: string; team_connect_user_id?: string }, res: Response) => {
  try {
    const organizerId = req.team_connect_user_id;
    if (!organizerId) {
      return sendUnauthorized(res, 'User not authenticated');
    }

    const { 
      staffId, 
      amount, 
      description, 
      staffBankAccountId, 
      paymentMethodType 
    } = req.body;

    if (!staffId) {
      return sendError(res, 'Staff ID is required', 400);
    }

    if (!amount || isNaN(parseFloat(amount)) || parseFloat(amount) <= 0) {
      return sendError(res, 'Valid amount is required', 400);
    }

    if (!staffBankAccountId) {
      return sendError(res, 'Staff bank account ID is required', 400);
    }

    const transferAmount = parseFloat(amount);
    
    const result = await createStaffBankTransfer(
      parseInt(organizerId),
      parseInt(staffId),
      transferAmount,
      description || 'Payment to staff',
      staffBankAccountId,
      paymentMethodType || 'ach'
    );

    if (result.success) {
      return sendSuccess(res, {
        transferId: result.transferId,
        status: result.status
      }, result.message);
    } else {
      return sendError(res, result.message || 'Failed to create staff transfer', 400);
    }
  } catch (error) {
    logger.error('Error creating staff transfer', { error });
    return sendError(res, 'Failed to create staff transfer', 500);
  }
};

/**
 * Create a withdrawal from user's wallet to their bank account
 */
/**
 * Create a transfer authorization
 * This is the first step in the transfer process and must be completed before creating a transfer
 */
export const createTransferAuthorizationController = async (req: Request & { userId?: string }, res: Response) => {
  try {
    const userId = req.userId;
    if (!userId) {
      return sendUnauthorized(res, 'User not authenticated');
    }

    const { 
      accessToken, 
      accountId, 
      type, 
      amount, 
      description, 
      user,
      network,
      achClass
    } = req.body;

    // Validate required fields
    if (!accessToken) {
      return sendError(res, 'Access token is required', 400);
    }

    if (!accountId) {
      return sendError(res, 'Account ID is required', 400);
    }

    if (!type || !Object.values(TransferType).includes(type)) {
      return sendError(res, 'Valid transfer type is required (credit or debit)', 400);
    }

    if (!amount || isNaN(parseFloat(amount)) || parseFloat(amount) <= 0) {
      return sendError(res, 'Valid amount is required', 400);
    }

    if (!user || !user.legal_name) {
      return sendError(res, 'User information is required', 400);
    }

    // Set default values for optional parameters
    const transferNetwork = network || TransferNetwork.Ach;
    const transferAchClass = achClass || ACHClass.Web;

    // Create the transfer authorization
    const result = await createTransferAuthorization(
      accessToken,
      accountId,
      type as TransferType,
      parseFloat(amount),
      description || 'Transfer',
      user,
      transferNetwork as TransferNetwork,
      transferAchClass as ACHClass
    );

    if (result.success) {
      return sendSuccess(res, {
        authorizationId: result.authorizationId,
        status: result.status,
        decision: result.decision
      }, result.message);
    } else {
      return sendError(res, result.message || 'Failed to create transfer authorization', 400);
    }
  } catch (error) {
    logger.error('Error creating transfer authorization', { error });
    return sendError(res, 'Failed to create transfer authorization', 500);
  }
};

export const createWithdrawal = async (req: Request & { userId?: string }, res: Response) => {
  try {
    const userId = req.userId;
    if (!userId) {
      return sendUnauthorized(res, 'User not authenticated');
    }

    const { amount, description, bankAccountId, pin, paymentMethodType } = req.body;

    // Validate amount format - ensure it's a valid decimal with 2 decimal places
    if (!amount || isNaN(parseFloat(amount)) || parseFloat(amount) <= 0) {
      return sendError(res, 'Valid amount is required', 400);
    }
    
    // Format the amount to have exactly 2 decimal places
    const transferAmount = parseFloat(parseFloat(amount).toFixed(2));
    
    // Ensure the amount has exactly 2 decimal places
    const amountStr = transferAmount.toString();
    const decimalParts = amountStr.split('.');
    if (decimalParts.length !== 2 || decimalParts[1].length !== 2) {
      return sendError(res, 'Withdrawal failed: amount must be a decimal with 2 places greater than 0, such as 0.10', 400);
    }

    if (!pin) {
      return sendError(res, 'PIN is required for wallet withdrawals', 400);
    }
    
    // Verify user's wallet and PIN
    const wallet = await getUserMasterWallet(parseInt(userId));
    
    if (!wallet) {
      return sendError(res, 'Wallet not found', 400);
    }

    if (!wallet.wallet_master_pin) {
      return sendError(res, 'Wallet PIN not set', 400);
    }

    const isPinValid = await comparePin(pin, wallet.wallet_master_pin);
    
    if (!isPinValid) {
      return sendError(res, 'Invalid wallet PIN', 400);
    }

    const currentBalance = parseFloat(wallet.balance.toString());
    
    if (currentBalance < transferAmount) {
      return sendError(res, `Insufficient wallet balance. Available: ${currentBalance.toFixed(2)}, Required: ${transferAmount.toFixed(2)}`, 400);
    }

    // Create the withdrawal transfer using Plaid
    const result = await createACHCreditTransfer(
      parseInt(userId), 
      transferAmount, 
      description || 'Withdraw money to bank account', 
      bankAccountId,
      paymentMethodType || 'ach'
    );

    if (result.success) {
      // Update wallet balance
      const newBalance = currentBalance - transferAmount;
      await updateWalletBalance(parseInt(userId), newBalance);
      
      return sendSuccess(res, {
        transferId: result.transferId,
        status: result.status,
        newBalance: newBalance.toFixed(2)
      }, result.message || 'Withdrawal initiated successfully');
    } else {
      return sendError(res, result.message || 'Failed to create withdrawal', 400);
    }
  } catch (error) {
    logger.error('Error creating withdrawal', { error });
    return sendError(res, 'Failed to create withdrawal', 500);
  }
};