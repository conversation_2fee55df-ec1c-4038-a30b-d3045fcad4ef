import { Request, Response } from 'express';
import {
  LinkTokenCreateRequest,
  AccountsGetRequest,
  ItemPublicTokenExchangeRequest,
  CountryCode,
  Products
} from 'plaid';
import { plaidClient } from '../config/plaidConfig';
import logger from '../utils/logger';
import { sendSuccess, sendError, sendRateLimitExceeded } from '../utils/response';
import { executeQuery, executeUpdate, executeQuerySingle } from '../utils/database';

// Simple in-memory rate limiter
const requestCounts: { [ip: string]: number } = {};
const MAX_REQUESTS_PER_MINUTE = 100;

// Rate limiting helper
const applyRateLimit = (req: Request, res: Response): boolean => {
  const ip = req.ip || 'unknown';
  requestCounts[ip] = (requestCounts[ip] || 0) + 1;

  if (requestCounts[ip] > MAX_REQUESTS_PER_MINUTE) {
    logger.warn(`Rate limit exceeded for IP: ${ip}`);
    sendRateLimitExceeded(res);
    return false;
  }

  setTimeout(() => {
    requestCounts[ip]--;
  }, 60000);

  return true;
};

// Interface for stored account data (using existing tbl_bank_accounts structure)
interface StoredAccount {
  id: string;
  user_id: string;
  plaid_account_id: string;
  account_mask: string;
  bank_name: string;
  account_type: string;
  routing_number: string;
  account_number_last4: string;
  is_primary: boolean;
  created_at: string;
  plaid_access_token?: string; // We'll add this to store access token per account
  plaid_item_id?: string; // We'll add this to store item ID per account
}

// Interface for master wallet (using existing tbl_masterwallet structure)
interface MasterWallet {
  id: string;
  user_id: string;
  plaid_access_token: string;
  wallet_master_pin: string;
  balance: number;
  status_id: number;
  create_at: string;
  last_updated: string;
}

// 1. Create Link Token
export const createLinkToken = async (req: Request, res: Response) => {
  try {
    if (!applyRateLimit(req, res)) return;

    const { client_name, country_codes, language, products, user } = req.body;

    // Validate required fields
    if (!client_name || !country_codes || !user?.client_user_id) {
      return sendError(res, 'Missing required fields', 400);
    }

    const request: LinkTokenCreateRequest = {
      client_name: client_name || 'PayConnect',
      country_codes: country_codes || [CountryCode.Us],
      language: language || 'en',
      products: products || [Products.Transactions, Products.Auth, Products.Transfer],
      user: {
        client_user_id: user.client_user_id
      }
    };

    const response = await plaidClient.linkTokenCreate(request);
    const linkToken = response.data;

    logger.info('Link token created successfully', { client_user_id: user.client_user_id });

    res.json({
      link_token: linkToken.link_token,
      expiration: linkToken.expiration,
      request_id: linkToken.request_id
    });

  } catch (err) {
    logger.error('Create link token error:', err);
    sendError(res, 'Failed to create link token', 500);
  }
};

// 2. Exchange Public Token
export const exchangePublicToken = async (req: Request, res: Response) => {
  try {
    if (!applyRateLimit(req, res)) return;

    const { public_token, accounts, institution, link_session_id } = req.body;
    const userId = (req as any).userId; // Assuming user ID comes from auth middleware

    if (!public_token) {
      return sendError(res, 'Missing public_token', 400);
    }

    if (!userId) {
      return sendError(res, 'User not authenticated', 401);
    }

    // Exchange public token for access token
    const exchangeRequest: ItemPublicTokenExchangeRequest = {
      public_token
    };

    const exchangeResponse = await plaidClient.itemPublicTokenExchange(exchangeRequest);
    const { access_token, item_id } = exchangeResponse.data;

    // Get account details from Plaid
    const accountsRequest: AccountsGetRequest = {
      access_token
    };

    const accountsResponse = await plaidClient.accountsGet(accountsRequest);
    const plaidAccounts = accountsResponse.data.accounts;
    const institutionData = accountsResponse.data.item;

    // Store accounts in existing tbl_bank_accounts table with access tokens
    for (const account of plaidAccounts) {
      // Check if this account already exists
      const existingAccount = await executeQuerySingle(
        'SELECT id FROM tbl_bank_accounts WHERE user_id = ? AND plaid_account_id = ?',
        [userId, account.account_id]
      );

      if (existingAccount) {
        // Update existing account with new access token
        await executeUpdate(
          `UPDATE tbl_bank_accounts
           SET plaid_access_token = ?, plaid_item_id = ?
           WHERE user_id = ? AND plaid_account_id = ?`,
          [access_token, item_id, userId, account.account_id]
        );
        logger.info('Updated existing account with new access token', {
          userId,
          accountId: account.account_id
        });
      } else {
        // Check if this is the first account for this user (make it primary)
        const existingAccounts = await executeQuery(
          'SELECT COUNT(*) as count FROM tbl_bank_accounts WHERE user_id = ?',
          [userId]
        );
        const isPrimary = existingAccounts[0].count === 0;

        // Insert new account into tbl_bank_accounts
        await executeUpdate(
          `INSERT INTO tbl_bank_accounts
           (user_id, plaid_account_id, account_mask, bank_name, account_type, routing_number, account_number_last4, is_primary, plaid_access_token, plaid_item_id)
           VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`,
          [
            userId,
            account.account_id,
            account.mask || '****',
            institution?.name || 'Unknown Bank',
            `${account.type}_${account.subtype}`,
            '', // We'll get routing number from auth endpoint if needed
            account.mask || '****',
            isPrimary,
            access_token, // Store access token with each account
            item_id
          ]
        );
        logger.info('Added new account with access token', {
          userId,
          accountId: account.account_id,
          bankName: institution?.name
        });
      }
    }

    logger.info('Public token exchanged successfully', { userId, item_id });

    res.json({
      access_token: access_token,
      item_id: item_id,
      request_id: exchangeResponse.data.request_id
    });

  } catch (err) {
    logger.error('Exchange public token error:', err);
    sendError(res, 'Failed to exchange public token', 500);
  }
};

// 3. Get Connected Accounts
export const getAccounts = async (req: Request, res: Response) => {
  try {
    if (!applyRateLimit(req, res)) return;

    const userId = (req as any).userId;

    if (!userId) {
      return sendError(res, 'User not authenticated', 401);
    }

    // Get stored accounts from existing tbl_bank_accounts table
    const storedAccounts = await executeQuery<StoredAccount>(
      'SELECT * FROM tbl_bank_accounts WHERE user_id = ? ORDER BY is_primary DESC, created_at ASC',
      [userId]
    );

    if (storedAccounts.length === 0) {
      logger.info(`No bank accounts found for user ${userId}`);
      return res.json([]);
    }

    const accountsWithBalances = [];

    // Get current balances from Plaid for each account using its own access token
    for (const storedAccount of storedAccounts) {
      try {
        // Skip accounts without access tokens
        if (!storedAccount.plaid_access_token) {
          logger.warn(`No access token for account ${storedAccount.plaid_account_id}`);
          // Include account without balance if no access token
          accountsWithBalances.push({
            id: storedAccount.id,
            account_id: storedAccount.plaid_account_id,
            name: storedAccount.bank_name + ' Account',
            official_name: storedAccount.bank_name + ' Account',
            mask: storedAccount.account_mask,
            type: storedAccount.account_type.split('_')[0] || 'depository',
            subtype: storedAccount.account_type.split('_')[1] || 'checking',
            institution_name: storedAccount.bank_name,
            balance: {
              available: null,
              current: null,
              currency: 'USD'
            },
            is_primary: storedAccount.is_primary,
            connected_at: storedAccount.created_at
          });
          continue;
        }

        const accountsRequest: AccountsGetRequest = {
          access_token: storedAccount.plaid_access_token
        };

        const accountsResponse = await plaidClient.accountsGet(accountsRequest);
        const plaidAccount = accountsResponse.data.accounts.find(
          acc => acc.account_id === storedAccount.plaid_account_id
        );

        if (plaidAccount) {
          accountsWithBalances.push({
            id: storedAccount.id,
            account_id: storedAccount.plaid_account_id,
            name: plaidAccount.name,
            official_name: plaidAccount.official_name || plaidAccount.name,
            mask: storedAccount.account_mask,
            type: plaidAccount.type,
            subtype: plaidAccount.subtype,
            institution_name: storedAccount.bank_name,
            balance: {
              available: plaidAccount.balances.available,
              current: plaidAccount.balances.current,
              currency: plaidAccount.balances.iso_currency_code || 'USD'
            },
            is_primary: storedAccount.is_primary,
            connected_at: storedAccount.created_at
          });
        }
      } catch (accountError) {
        logger.error(`Error fetching balance for account ${storedAccount.plaid_account_id}:`, accountError);
        // Include account without balance if Plaid call fails
        accountsWithBalances.push({
          id: storedAccount.id,
          account_id: storedAccount.plaid_account_id,
          name: storedAccount.bank_name + ' Account',
          official_name: storedAccount.bank_name + ' Account',
          mask: storedAccount.account_mask,
          type: storedAccount.account_type.split('_')[0] || 'depository',
          subtype: storedAccount.account_type.split('_')[1] || 'checking',
          institution_name: storedAccount.bank_name,
          balance: {
            available: null,
            current: null,
            currency: 'USD'
          },
          is_primary: storedAccount.is_primary,
          connected_at: storedAccount.created_at
        });
      }
    }

    logger.info('Accounts retrieved successfully', { userId, count: accountsWithBalances.length });
    res.json(accountsWithBalances);

  } catch (err) {
    logger.error('Get accounts error:', err);
    sendError(res, 'Failed to retrieve accounts', 500);
  }
};

// 4. Set Primary Account
export const setPrimaryAccount = async (req: Request, res: Response) => {
  try {
    if (!applyRateLimit(req, res)) return;

    const { accountId } = req.params;
    const userId = (req as any).userId;

    if (!userId) {
      return sendError(res, 'User not authenticated', 401);
    }

    if (!accountId) {
      return sendError(res, 'Account ID is required', 400);
    }

    // Verify account belongs to user
    const account = await executeQuerySingle(
      'SELECT id FROM tbl_bank_accounts WHERE id = ? AND user_id = ?',
      [accountId, userId]
    );

    if (!account) {
      return sendError(res, 'Account not found', 404);
    }

    // Update all accounts to not primary, then set the specified one as primary
    await executeUpdate(
      'UPDATE tbl_bank_accounts SET is_primary = FALSE WHERE user_id = ?',
      [userId]
    );

    await executeUpdate(
      'UPDATE tbl_bank_accounts SET is_primary = TRUE WHERE id = ? AND user_id = ?',
      [accountId, userId]
    );

    logger.info('Primary account updated successfully', { userId, accountId });
    res.json({ success: true });

  } catch (err) {
    logger.error('Set primary account error:', err);
    sendError(res, 'Failed to set primary account', 500);
  }
};

// 5. Disconnect Account
export const disconnectAccount = async (req: Request, res: Response) => {
  try {
    if (!applyRateLimit(req, res)) return;

    const { accountId } = req.params;
    const userId = (req as any).userId;

    if (!userId) {
      return sendError(res, 'User not authenticated', 401);
    }

    if (!accountId) {
      return sendError(res, 'Account ID is required', 400);
    }

    // Verify account belongs to user and get account details
    const account = await executeQuerySingle<StoredAccount>(
      'SELECT * FROM tbl_bank_accounts WHERE id = ? AND user_id = ?',
      [accountId, userId]
    );

    if (!account) {
      return sendError(res, 'Account not found', 404);
    }

    // Remove account from database (access token is stored with the account)
    await executeUpdate(
      'DELETE FROM tbl_bank_accounts WHERE id = ? AND user_id = ?',
      [accountId, userId]
    );

    // If this was the primary account, set another account as primary
    if (account.is_primary) {
      const remainingAccounts = await executeQuery(
        'SELECT id FROM tbl_bank_accounts WHERE user_id = ? ORDER BY created_at ASC LIMIT 1',
        [userId]
      );

      if (remainingAccounts.length > 0) {
        await executeUpdate(
          'UPDATE tbl_bank_accounts SET is_primary = TRUE WHERE id = ?',
          [remainingAccounts[0].id]
        );
      }
    }

    logger.info('Account disconnected successfully', { userId, accountId });
    res.json({ success: true });

  } catch (err) {
    logger.error('Disconnect account error:', err);
    sendError(res, 'Failed to disconnect account', 500);
  }
};

// Legacy controller for backward compatibility
export const plaidController = exchangePublicToken;
