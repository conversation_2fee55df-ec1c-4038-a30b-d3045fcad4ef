import { Request, Response } from 'express';
import { PlaidSandboxManager, TransferSimulationOptions } from '../services/plaidSandboxManager';
import { sendSuccess, sendError, sendUnauthorized } from '../utils/response';
import logger from '../utils/logger';

/**
 * Mark a transfer as successful (sandbox only)
 */
export const markTransferAsSuccessful = async (req: Request & { userId?: string }, res: Response) => {
  try {
    const userId = req.userId;
    if (!userId) {
      return sendUnauthorized(res, 'Authentication required');
    }

    const { transferId, delay = 0 } = req.body;

    if (!transferId) {
      return sendError(res, 'Transfer ID is required', 400);
    }

    logger.info('Manual transfer success requested', { userId, transferId, delay });

    const result = await PlaidSandboxManager.markTransferAsSuccessful(transferId, delay);

    if (result.success) {
      sendSuccess(res, result, 'Transfer marked as successful');
    } else {
      sendError(res, result.message || 'Failed to mark transfer as successful', 400);
    }

  } catch (error: any) {
    logger.error('Error in markTransferAsSuccessful controller', { error: error.message });
    sendError(res, 'Internal server error', 500);
  }
};

/**
 * Mark a transfer as failed (sandbox only)
 */
export const markTransferAsFailed = async (req: Request & { userId?: string }, res: Response) => {
  try {
    const userId = req.userId;
    if (!userId) {
      return sendUnauthorized(res, 'Authentication required');
    }

    const {
      transferId,
      failureReason = 'insufficient_funds',
      customReason,
      delay = 0
    } = req.body;

    if (!transferId) {
      return sendError(res, 'Transfer ID is required', 400);
    }

    logger.info('Manual transfer failure requested', { 
      userId, 
      transferId, 
      failureReason, 
      customReason,
      delay 
    });

    const result = await PlaidSandboxManager.markTransferAsFailed(
      transferId, 
      failureReason,
      customReason,
      delay
    );

    if (result.success) {
      sendSuccess(res, result, 'Transfer marked as failed');
    } else {
      sendError(res, result.message || 'Failed to mark transfer as failed', 400);
    }

  } catch (error: any) {
    logger.error('Error in markTransferAsFailed controller', { error: error.message });
    sendError(res, 'Internal server error', 500);
  }
};

/**
 * Mark a transfer as returned (sandbox only)
 */
export const markTransferAsReturned = async (req: Request & { userId?: string }, res: Response) => {
  try {
    const userId = req.userId;
    if (!userId) {
      return sendUnauthorized(res, 'Authentication required');
    }

    const { 
      transferId, 
      returnReason = 'Account closed',
      delay = 0 
    } = req.body;

    if (!transferId) {
      return sendError(res, 'Transfer ID is required', 400);
    }

    logger.info('Manual transfer return requested', { 
      userId, 
      transferId, 
      returnReason,
      delay 
    });

    const result = await PlaidSandboxManager.markTransferAsReturned(transferId, returnReason, delay);

    if (result.success) {
      sendSuccess(res, result, 'Transfer marked as returned');
    } else {
      sendError(res, result.message || 'Failed to mark transfer as returned', 400);
    }

  } catch (error: any) {
    logger.error('Error in markTransferAsReturned controller', { error: error.message });
    sendError(res, 'Internal server error', 500);
  }
};

/**
 * Cancel a transfer (sandbox only)
 */
export const cancelTransfer = async (req: Request & { userId?: string }, res: Response) => {
  try {
    const userId = req.userId;
    if (!userId) {
      return sendUnauthorized(res, 'Authentication required');
    }

    const { transferId, delay = 0 } = req.body;

    if (!transferId) {
      return sendError(res, 'Transfer ID is required', 400);
    }

    logger.info('Manual transfer cancellation requested', { userId, transferId, delay });

    const result = await PlaidSandboxManager.cancelTransfer(transferId, delay);

    if (result.success) {
      sendSuccess(res, result, 'Transfer cancelled successfully');
    } else {
      sendError(res, result.message || 'Failed to cancel transfer', 400);
    }

  } catch (error: any) {
    logger.error('Error in cancelTransfer controller', { error: error.message });
    sendError(res, 'Internal server error', 500);
  }
};

/**
 * Simulate a transfer with specific outcome (sandbox only)
 */
export const simulateTransfer = async (req: Request & { userId?: string }, res: Response) => {
  try {
    const userId = req.userId;
    if (!userId) {
      return sendUnauthorized(res, 'Authentication required');
    }

    const options: TransferSimulationOptions = req.body;

    if (!options.transferId || !options.eventType) {
      return sendError(res, 'Transfer ID and event type are required', 400);
    }

    if (!['success', 'failure', 'return', 'cancel'].includes(options.eventType)) {
      return sendError(res, 'Invalid event type. Must be: success, failure, return, or cancel', 400);
    }

    logger.info('Transfer simulation requested', { userId, options });

    const result = await PlaidSandboxManager.simulateTransfer(options);

    if (result.success) {
      sendSuccess(res, result, `Transfer simulation completed: ${options.eventType}`);
    } else {
      sendError(res, result.message || 'Failed to simulate transfer', 400);
    }

  } catch (error: any) {
    logger.error('Error in simulateTransfer controller', { error: error.message });
    sendError(res, 'Internal server error', 500);
  }
};

/**
 * Get transfer status from Plaid
 */
export const getTransferStatus = async (req: Request & { userId?: string }, res: Response) => {
  try {
    const userId = req.userId;
    if (!userId) {
      return sendUnauthorized(res, 'Authentication required');
    }

    const { transferId } = req.params;

    if (!transferId) {
      return sendError(res, 'Transfer ID is required', 400);
    }

    logger.info('Transfer status check requested', { userId, transferId });

    const result = await PlaidSandboxManager.getTransferStatus(transferId);

    if (result.success) {
      sendSuccess(res, result, 'Transfer status retrieved successfully');
    } else {
      sendError(res, result.message || 'Failed to get transfer status', 400);
    }

  } catch (error: any) {
    logger.error('Error in getTransferStatus controller', { error: error.message });
    sendError(res, 'Internal server error', 500);
  }
};

/**
 * Get available failure reasons for testing
 */
export const getAvailableFailureReasons = async (req: Request & { userId?: string }, res: Response) => {
  try {
    const userId = req.userId;
    if (!userId) {
      return sendUnauthorized(res, 'Authentication required');
    }

    const failureReasons = PlaidSandboxManager.getAvailableFailureReasons();

    sendSuccess(res, { failureReasons }, 'Available failure reasons retrieved successfully');

  } catch (error: any) {
    logger.error('Error in getAvailableFailureReasons controller', { error: error.message });
    sendError(res, 'Internal server error', 500);
  }
};

/**
 * Batch simulate multiple transfers
 */
export const batchSimulateTransfers = async (req: Request & { userId?: string }, res: Response) => {
  try {
    const userId = req.userId;
    if (!userId) {
      return sendUnauthorized(res, 'Authentication required');
    }

    const { simulations } = req.body;

    if (!Array.isArray(simulations) || simulations.length === 0) {
      return sendError(res, 'Simulations array is required and must not be empty', 400);
    }

    // Validate each simulation
    for (const sim of simulations) {
      if (!sim.transferId || !sim.eventType) {
        return sendError(res, 'Each simulation must have transferId and eventType', 400);
      }
      if (!['success', 'failure', 'return', 'cancel'].includes(sim.eventType)) {
        return sendError(res, 'Invalid event type in simulation. Must be: success, failure, return, or cancel', 400);
      }
    }

    logger.info('Batch transfer simulation requested', { userId, simulationCount: simulations.length });

    const results = await PlaidSandboxManager.batchSimulateTransfers(simulations);

    sendSuccess(res, { results }, `Batch simulation completed for ${simulations.length} transfers`);

  } catch (error: any) {
    logger.error('Error in batchSimulateTransfers controller', { error: error.message });
    sendError(res, 'Internal server error', 500);
  }
};
