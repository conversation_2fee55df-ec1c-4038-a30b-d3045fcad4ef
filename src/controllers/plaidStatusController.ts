import { Request, Response } from 'express';
import { 
  syncAllPendingTransfers, 
  syncTransferById, 
  getSyncStatistics 
} from '../services/plaidStatusSyncService';
import { getEnhancedWalletBalance, getUserPendingHolds } from '../services/enhancedPendingBalanceService';
import { sendSuccess, sendError, sendUnauthorized } from '../utils/response';
import logger from '../utils/logger';

/**
 * Manually sync all pending transfers
 */
export const syncPendingTransfers = async (req: Request & { userId?: string }, res: Response) => {
  try {
    const userId = req.userId;
    if (!userId) {
      return sendUnauthorized(res, 'Authentication required');
    }

    logger.info('Manual sync of pending transfers requested', { userId });

    const syncResult = await syncAllPendingTransfers();

    sendSuccess(res, {
      syncResult,
      message: `Sync completed: ${syncResult.confirmed} confirmed, ${syncResult.failed} failed, ${syncResult.errors} errors`
    }, 'Pending transfers synced successfully');

  } catch (error) {
    logger.error('Error in manual sync controller', {
      error: error instanceof Error ? error.message : 'Unknown error',
      userId: req.userId
    });
    sendError(res, 'Failed to sync pending transfers', 500);
  }
};

/**
 * Sync a specific transfer by ID
 */
export const syncSpecificTransfer = async (req: Request & { userId?: string }, res: Response) => {
  try {
    const userId = req.userId;
    const { transferId } = req.params;

    if (!userId) {
      return sendUnauthorized(res, 'Authentication required');
    }

    if (!transferId) {
      return sendError(res, 'Transfer ID is required', 400);
    }

    logger.info('Manual sync of specific transfer requested', { userId, transferId });

    const syncResult = await syncTransferById(transferId);

    if (syncResult.success) {
      sendSuccess(res, syncResult, 'Transfer synced successfully');
    } else {
      sendError(res, syncResult.message || 'Failed to sync transfer', 400);
    }

  } catch (error) {
    logger.error('Error in specific transfer sync controller', {
      error: error instanceof Error ? error.message : 'Unknown error',
      userId: req.userId,
      transferId: req.params.transferId
    });
    sendError(res, 'Failed to sync transfer', 500);
  }
};

/**
 * Get enhanced wallet status including pending balances
 */
export const getWalletStatus = async (req: Request & { userId?: string }, res: Response) => {
  try {
    const userId = req.userId;
    if (!userId) {
      return sendUnauthorized(res, 'Authentication required');
    }

    // Get enhanced wallet balance
    const balanceInfo = await getEnhancedWalletBalance(parseInt(userId));
    if (!balanceInfo) {
      return sendError(res, 'Wallet not found', 404);
    }

    // Get pending holds
    const pendingHolds = await getUserPendingHolds(parseInt(userId));

    // Get sync statistics
    const syncStats = await getSyncStatistics();

    sendSuccess(res, {
      balance: {
        total: balanceInfo.balance,
        available: balanceInfo.available_balance,
        pending: balanceInfo.pending_balance,
        blocked: balanceInfo.blocked_balance,
        formattedTotal: `$${balanceInfo.balance.toFixed(2)}`,
        formattedAvailable: `$${balanceInfo.available_balance.toFixed(2)}`,
        formattedPending: `$${balanceInfo.pending_balance.toFixed(2)}`,
        formattedBlocked: `$${balanceInfo.blocked_balance.toFixed(2)}`
      },
      pendingHolds: pendingHolds.map(hold => ({
        id: hold.id,
        type: hold.type,
        amount: hold.amount,
        formattedAmount: `$${hold.amount.toFixed(2)}`,
        description: hold.description,
        plaidTransferId: hold.plaid_transfer_id,
        status: hold.status,
        createdAt: hold.created_at,
        expiresAt: hold.expires_at
      })),
      syncStats,
      status: {
        hasPendingTransactions: pendingHolds.length > 0,
        availableForSpending: balanceInfo.available_balance,
        totalPendingAmount: pendingHolds.reduce((sum, hold) => sum + hold.amount, 0)
      }
    }, 'Wallet status retrieved successfully');

  } catch (error) {
    logger.error('Error getting wallet status', {
      error: error instanceof Error ? error.message : 'Unknown error',
      userId: req.userId
    });
    sendError(res, 'Failed to get wallet status', 500);
  }
};

/**
 * Get transfer status by ID
 */
export const getTransferStatusById = async (req: Request & { userId?: string }, res: Response) => {
  try {
    const userId = req.userId;
    const { transferId } = req.params;

    if (!userId) {
      return sendUnauthorized(res, 'Authentication required');
    }

    if (!transferId) {
      return sendError(res, 'Transfer ID is required', 400);
    }

    // Import the transfer status function
    const { getTransferStatus } = await import('../services/plaidTransferService');
    
    const statusResult = await getTransferStatus(transferId);

    if (statusResult.success) {
      sendSuccess(res, {
        transferId,
        status: statusResult.status,
        failureReason: statusResult.failureReason,
        lastUpdated: new Date().toISOString(),
        details: statusResult
      }, 'Transfer status retrieved successfully');
    } else {
      sendError(res, statusResult.message || 'Failed to get transfer status', 400);
    }

  } catch (error) {
    logger.error('Error getting transfer status', {
      error: error instanceof Error ? error.message : 'Unknown error',
      userId: req.userId,
      transferId: req.params.transferId
    });
    sendError(res, 'Failed to get transfer status', 500);
  }
};

/**
 * Get system sync statistics (admin only)
 */
export const getSystemSyncStats = async (req: Request & { userId?: string }, res: Response) => {
  try {
    const userId = req.userId;
    if (!userId) {
      return sendUnauthorized(res, 'Authentication required');
    }

    // TODO: Add admin role check here
    // For now, allowing all authenticated users

    const syncStats = await getSyncStatistics();

    sendSuccess(res, {
      statistics: syncStats,
      timestamp: new Date().toISOString(),
      systemStatus: {
        healthy: syncStats.pendingHolds < 100 && syncStats.failedTransfers < 10,
        pendingHoldsStatus: syncStats.pendingHolds < 50 ? 'normal' : syncStats.pendingHolds < 100 ? 'warning' : 'critical',
        failedTransfersStatus: syncStats.failedTransfers < 5 ? 'normal' : syncStats.failedTransfers < 10 ? 'warning' : 'critical'
      }
    }, 'System sync statistics retrieved successfully');

  } catch (error) {
    logger.error('Error getting system sync stats', {
      error: error instanceof Error ? error.message : 'Unknown error',
      userId: req.userId
    });
    sendError(res, 'Failed to get system statistics', 500);
  }
};

/**
 * Force cleanup of expired holds (admin only)
 */
export const forceCleanupExpiredHolds = async (req: Request & { userId?: string }, res: Response) => {
  try {
    const userId = req.userId;
    if (!userId) {
      return sendUnauthorized(res, 'Authentication required');
    }

    // TODO: Add admin role check here
    // For now, allowing all authenticated users

    const { cleanupExpiredHolds } = await import('../services/enhancedPendingBalanceService');
    const cleanupResult = await cleanupExpiredHolds();

    logger.info('Manual cleanup of expired holds completed', {
      userId,
      cleanedCount: cleanupResult.cleaned
    });

    sendSuccess(res, {
      cleanedCount: cleanupResult.cleaned,
      message: `Cleaned up ${cleanupResult.cleaned} expired holds`
    }, 'Expired holds cleanup completed');

  } catch (error) {
    logger.error('Error in manual cleanup controller', {
      error: error instanceof Error ? error.message : 'Unknown error',
      userId: req.userId
    });
    sendError(res, 'Failed to cleanup expired holds', 500);
  }
};
