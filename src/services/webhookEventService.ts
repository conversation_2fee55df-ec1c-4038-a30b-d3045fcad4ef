import { executeUpdate, executeQuery, executeQuery<PERSON>ingle } from '../utils/database';
import logger from '../utils/logger';
import { WebhookEvent } from '../models/webhookEvent';

/**
 * Store a webhook event in the database
 * @param webhookType Type of webhook (e.g., 'TRANSFER', 'ITEM', 'AUTH')
 * @param webhookCode Webhook code (e.g., 'TRANSFER_CREATED', 'TRANSFER_PENDING')
 * @param rawData Raw webhook data
 * @param processedResult Result of processing the webhook
 * @param verified Whether the webhook signature was verified
 * @returns The ID of the stored webhook event
 */
export async function storeWebhookEvent(
  webhookType: string,
  webhookCode: string,
  rawData: any,
  processedResult: any = null,
  verified: boolean = false
): Promise<number> {
  try {
    // Generate a unique event ID if not provided
    const eventId = rawData.webhook_id || 
                   rawData.event_id || 
                   `webhook_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`;
    
    // Extract relevant IDs from the webhook data
    const transferId = rawData.transfer_id || rawData.data?.transfer_id || null;
    const itemId = rawData.item_id || rawData.data?.item_id || null;
    const accountId = rawData.account_id || rawData.data?.account_id || null;
    const status = rawData.new_transfer_status || 
                  rawData.new_status || 
                  rawData.status || 
                  null;
    
    // Store the webhook event
    const result = await executeUpdate(
      `INSERT INTO tbl_webhook_events
       (webhook_type, webhook_code, event_id, transfer_id, item_id, account_id, status, 
        raw_data, processed_result, verified, created_at)
       VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW())`,
      [
        webhookType,
        webhookCode,
        eventId,
        transferId,
        itemId,
        accountId,
        status,
        JSON.stringify(rawData),
        processedResult ? JSON.stringify(processedResult) : null,
        verified,
        // Current timestamp is added by MySQL
      ]
    );
    
    logger.info('Webhook event stored', {
      id: result.insertId,
      webhookType,
      webhookCode,
      eventId,
      transferId
    });
    
    return result.insertId;
    
  } catch (error) {
    logger.error('Error storing webhook event', {
      webhookType,
      webhookCode,
      error
    });
    
    // Return 0 to indicate failure
    return 0;
  }
}

/**
 * Update the processed result of a webhook event
 * @param eventId ID of the webhook event
 * @param processedResult Result of processing the webhook
 * @returns Whether the update was successful
 */
export async function updateWebhookEventResult(
  eventId: string,
  processedResult: any
): Promise<boolean> {
  try {
    await executeUpdate(
      `UPDATE tbl_webhook_events
       SET processed_result = ?
       WHERE event_id = ?`,
      [
        JSON.stringify(processedResult),
        eventId
      ]
    );
    
    return true;
    
  } catch (error) {
    logger.error('Error updating webhook event result', {
      eventId,
      error
    });
    
    return false;
  }
}

/**
 * Get webhook events by transfer ID
 * @param transferId ID of the transfer
 * @param limit Maximum number of events to return
 * @param offset Offset for pagination
 * @returns Array of webhook events
 */
export async function getWebhookEventsByTransferId(
  transferId: string,
  limit: number = 50,
  offset: number = 0
): Promise<WebhookEvent[]> {
  try {
    const events = await executeQuery(
      `SELECT id, webhook_type, webhook_code, event_id, transfer_id, item_id, account_id,
              status, raw_data, processed_result, verified, created_at
       FROM tbl_webhook_events
       WHERE transfer_id = ?
       ORDER BY created_at DESC
       LIMIT ? OFFSET ?`,
      [transferId, limit, offset]
    );
    
    // Convert raw_data and processed_result from strings to objects
    return (Array.isArray(events) ? events : events ? [events] : []).map(event => ({
      ...event,
      rawData: typeof event.raw_data === 'string' ? JSON.parse(event.raw_data) : event.raw_data,
      processedResult: event.processed_result && typeof event.processed_result === 'string' 
        ? JSON.parse(event.processed_result) 
        : event.processed_result || null
    }));
    
  } catch (error) {
    logger.error('Error getting webhook events by transfer ID', {
      transferId,
      error
    });
    
    return [];
  }
}

/**
 * Get webhook events by type and code
 * @param webhookType Type of webhook
 * @param webhookCode Webhook code
 * @param limit Maximum number of events to return
 * @param offset Offset for pagination
 * @returns Array of webhook events
 */
export async function getWebhookEventsByType(
  webhookType: string,
  webhookCode?: string,
  limit: number = 50,
  offset: number = 0
): Promise<WebhookEvent[]> {
  try {
    let query = `
      SELECT id, webhook_type, webhook_code, event_id, transfer_id, item_id, account_id,
             status, raw_data, processed_result, verified, created_at
      FROM tbl_webhook_events
      WHERE webhook_type = ?
    `;
    
    const params: any[] = [webhookType];
    
    if (webhookCode) {
      query += ` AND webhook_code = ?`;
      params.push(webhookCode);
    }
    
    query += ` ORDER BY created_at DESC LIMIT ? OFFSET ?`;
    params.push(limit, offset);
    
    const events = await executeQuery(query, params);
    
    // Convert raw_data and processed_result from strings to objects
    return (Array.isArray(events) ? events : events ? [events] : []).map(event => ({
      ...event,
      rawData: typeof event.raw_data === 'string' ? JSON.parse(event.raw_data) : event.raw_data,
      processedResult: event.processed_result && typeof event.processed_result === 'string' 
        ? JSON.parse(event.processed_result) 
        : event.processed_result || null
    }));
    
  } catch (error) {
    logger.error('Error getting webhook events by type', {
      webhookType,
      webhookCode,
      error
    });
    
    return [];
  }
}

/**
 * Get recent webhook events
 * @param limit Maximum number of events to return
 * @param offset Offset for pagination
 * @returns Array of webhook events
 */
export async function getRecentWebhookEvents(
  limit: number = 50,
  offset: number = 0
): Promise<WebhookEvent[]> {
  try {
    const events = await executeQuery(
      `SELECT id, webhook_type, webhook_code, event_id, transfer_id, item_id, account_id,
              status, raw_data, processed_result, verified, created_at
       FROM tbl_webhook_events
       ORDER BY created_at DESC
       LIMIT ? OFFSET ?`,
      [limit, offset]
    );
    
    // Convert raw_data and processed_result from strings to objects
    return (Array.isArray(events) ? events : events ? [events] : []).map(event => ({
      ...event,
      rawData: typeof event.raw_data === 'string' ? JSON.parse(event.raw_data) : event.raw_data,
      processedResult: event.processed_result && typeof event.processed_result === 'string' 
        ? JSON.parse(event.processed_result) 
        : event.processed_result || null
    }));
    
  } catch (error) {
    logger.error('Error getting recent webhook events', {
      error
    });
    
    return [];
  }
}

/**
 * Get webhook event by ID
 * @param id ID of the webhook event
 * @returns Webhook event or null if not found
 */
export async function getWebhookEventById(id: number): Promise<WebhookEvent | null> {
  try {
    const event = await executeQuerySingle(
      `SELECT id, webhook_type, webhook_code, event_id, transfer_id, item_id, account_id,
              status, raw_data, processed_result, verified, created_at
       FROM tbl_webhook_events
       WHERE id = ?`,
      [id]
    );
    
    if (!event) {
      return null;
    }
    
    // Convert raw_data and processed_result from strings to objects
    return {
      ...event,
      rawData: typeof event.raw_data === 'string' ? JSON.parse(event.raw_data) : event.raw_data,
      processedResult: event.processed_result && typeof event.processed_result === 'string' 
        ? JSON.parse(event.processed_result) 
        : event.processed_result || null
    };
    
  } catch (error) {
    logger.error('Error getting webhook event by ID', {
      id,
      error
    });
    
    return null;
  }
}

/**
 * Delete old webhook events
 * @param daysToKeep Number of days to keep webhook events
 * @returns Number of deleted events
 */
export async function deleteOldWebhookEvents(daysToKeep: number = 90): Promise<number> {
  try {
    const result = await executeUpdate(
      `DELETE FROM tbl_webhook_events
       WHERE created_at < DATE_SUB(NOW(), INTERVAL ? DAY)`,
      [daysToKeep]
    );
    
    logger.info(`Deleted ${result.affectedRows} old webhook events`, {
      daysToKeep,
      deletedCount: result.affectedRows
    });
    
    return result.affectedRows;
    
  } catch (error) {
    logger.error('Error deleting old webhook events', {
      daysToKeep,
      error
    });
    
    return 0;
  }
}