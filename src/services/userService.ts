import { 
  executeQuery, 
  executeUpdate, 
  executeQuerySingle, 
  executeTransaction, 
  recordExists 
} from '../utils/database';
import { hashPassword, comparePassword } from '../models/user';
import logger from '../utils/logger';

export interface UserData {
  id?: number;
  email: string;
  firstname?: string;
  lastname?: string;
  password_hash?: string;
  created_at?: Date;
  updated_at?: Date;
}

export interface CreateUserData {
  email: string;
  password: string;
  firstname?: string;
  lastname?: string;
}

export interface UpdateUserData {
  firstname?: string;
  lastname?: string;
  email?: string;
}

/**
 * Get user by email
 * @param email - User email
 * @returns Promise<UserData | null>
 */
export async function getUserByEmail(email: string): Promise<UserData | null> {
  try {
    const user = await executeQuerySingle<UserData>(
      'SELECT id, email, firstname, lastname, created_at, updated_at FROM users WHERE email = ?',
      [email]
    );
    return user;
  } catch (error) {
    logger.error('Error getting user by email:', { email, error });
    throw error;
  }
}

/**
 * Get user by ID
 * @param id - User ID
 * @returns Promise<UserData | null>
 */
export async function getUserById(id: number): Promise<UserData | null> {
  try {
    const user = await executeQuerySingle<UserData>(
      'SELECT id, email, firstname, lastname, created_at, updated_at FROM users WHERE id = ?',
      [id]
    );
    return user;
  } catch (error) {
    logger.error('Error getting user by ID:', { id, error });
    throw error;
  }
}

/**
 * Create a new user
 * @param userData - User data to create
 * @returns Promise<UserData>
 */
export async function createUser(userData: CreateUserData): Promise<UserData> {
  try {
    // Check if user already exists
    const existingUser = await getUserByEmail(userData.email);
    if (existingUser) {
      throw new Error('User with this email already exists');
    }

    // Hash the password
    const passwordHash = await hashPassword(userData.password);

    // Insert user
    const result = await executeUpdate(
      `INSERT INTO users (email, password_hash, firstname, lastname, created_at, updated_at) 
       VALUES (?, ?, ?, ?, NOW(), NOW())`,
      [userData.email, passwordHash, userData.firstname || null, userData.lastname || null]
    );

    // Get the created user
    const newUser = await getUserById(result.insertId);
    if (!newUser) {
      throw new Error('Failed to retrieve created user');
    }

    logger.info('User created successfully:', { userId: newUser.id, email: newUser.email });
    return newUser;
  } catch (error) {
    logger.error('Error creating user:', { userData: { ...userData, password: '[REDACTED]' }, error });
    throw error;
  }
}

/**
 * Update user data
 * @param id - User ID
 * @param updateData - Data to update
 * @returns Promise<UserData | null>
 */
export async function updateUser(id: number, updateData: UpdateUserData): Promise<UserData | null> {
  try {
    // Check if user exists
    const existingUser = await getUserById(id);
    if (!existingUser) {
      return null;
    }

    // Build dynamic update query
    const updateFields: string[] = [];
    const updateValues: any[] = [];

    if (updateData.firstname !== undefined) {
      updateFields.push('firstname = ?');
      updateValues.push(updateData.firstname);
    }

    if (updateData.lastname !== undefined) {
      updateFields.push('lastname = ?');
      updateValues.push(updateData.lastname);
    }

    if (updateData.email !== undefined) {
      // Check if new email is already taken
      const emailExists = await recordExists(
        'SELECT COUNT(*) as count FROM users WHERE email = ? AND id != ?',
        [updateData.email, id]
      );
      
      if (emailExists) {
        throw new Error('Email is already taken by another user');
      }

      updateFields.push('email = ?');
      updateValues.push(updateData.email);
    }

    if (updateFields.length === 0) {
      return existingUser; // No updates needed
    }

    updateFields.push('updated_at = NOW()');
    updateValues.push(id);

    const query = `UPDATE users SET ${updateFields.join(', ')} WHERE id = ?`;
    await executeUpdate(query, updateValues);

    // Return updated user
    const updatedUser = await getUserById(id);
    logger.info('User updated successfully:', { userId: id, updateData });
    return updatedUser;
  } catch (error) {
    logger.error('Error updating user:', { id, updateData, error });
    throw error;
  }
}

/**
 * Delete user
 * @param id - User ID
 * @returns Promise<boolean>
 */
export async function deleteUser(id: number): Promise<boolean> {
  try {
    const result = await executeUpdate('DELETE FROM users WHERE id = ?', [id]);
    const deleted = result.affectedRows > 0;
    
    if (deleted) {
      logger.info('User deleted successfully:', { userId: id });
    } else {
      logger.warn('User not found for deletion:', { userId: id });
    }
    
    return deleted;
  } catch (error) {
    logger.error('Error deleting user:', { id, error });
    throw error;
  }
}

/**
 * Authenticate user with email and password
 * @param email - User email
 * @param password - User password
 * @returns Promise<UserData | null>
 */
export async function authenticateUser(email: string, password: string): Promise<UserData | null> {
  try {
    // Get user with password hash
    const user = await executeQuerySingle<UserData & { password_hash: string }>(
      'SELECT id, email, firstname, lastname, password_hash, created_at, updated_at FROM users WHERE email = ?',
      [email]
    );

    if (!user || !user.password_hash) {
      return null;
    }

    // Verify password
    const isValidPassword = await comparePassword(password, user.password_hash);
    if (!isValidPassword) {
      return null;
    }

    // Return user without password hash
    const { password_hash, ...userWithoutPassword } = user;
    return userWithoutPassword;
  } catch (error) {
    logger.error('Error authenticating user:', { email, error });
    throw error;
  }
}

/**
 * Get paginated users
 * @param page - Page number (1-based)
 * @param limit - Number of users per page
 * @returns Promise<{ users: UserData[], total: number }>
 */
export async function getPaginatedUsers(
  page: number = 1, 
  limit: number = 10
): Promise<{ users: UserData[], total: number }> {
  try {
    const offset = (page - 1) * limit;

    // Get total count
    const countResult = await executeQuerySingle<{ total: number }>(
      'SELECT COUNT(*) as total FROM users'
    );
    const total = countResult?.total || 0;

    // Get paginated users
    const users = await executeQuery<UserData>(
      'SELECT id, email, firstname, lastname, created_at, updated_at FROM users ORDER BY created_at DESC LIMIT ? OFFSET ?',
      [limit, offset]
    );

    return { users, total };
  } catch (error) {
    logger.error('Error getting paginated users:', { page, limit, error });
    throw error;
  }
}

/**
 * Example of using transaction for complex operations
 * @param userData - User data
 * @param profileData - Additional profile data
 * @returns Promise<UserData>
 */
export async function createUserWithProfile(
  userData: CreateUserData,
  profileData: { bio?: string; avatar_url?: string }
): Promise<UserData> {
  try {
    const passwordHash = await hashPassword(userData.password);

    const queries = [
      {
        query: `INSERT INTO users (email, password_hash, firstname, lastname, created_at, updated_at) 
                VALUES (?, ?, ?, ?, NOW(), NOW())`,
        params: [userData.email, passwordHash, userData.firstname || null, userData.lastname || null]
      },
      {
        query: `INSERT INTO user_profiles (user_id, bio, avatar_url, created_at, updated_at) 
                VALUES (LAST_INSERT_ID(), ?, ?, NOW(), NOW())`,
        params: [profileData.bio || null, profileData.avatar_url || null]
      }
    ];

    const results = await executeTransaction(queries);
    const userId = (results[0] as any).insertId;

    const newUser = await getUserById(userId);
    if (!newUser) {
      throw new Error('Failed to retrieve created user');
    }

    logger.info('User with profile created successfully:', { userId, email: userData.email });
    return newUser;
  } catch (error) {
    logger.error('Error creating user with profile:', { userData: { ...userData, password: '[REDACTED]' }, profileData, error });
    throw error;
  }
}
