import { executeUpdate, executeQuerySingle, executeQuery } from '../utils/database';
import logger from '../utils/logger';
import { 
  createTransferTracking, 
  updateWalletTransactionWithEventId,
  updatePendingHoldWithEventId,
  recordEventCorrelation 
} from './plaidEventTrackingService';

/**
 * Enhanced wallet transaction creation with event ID tracking
 */
export async function createWalletTransactionWithEventTracking(
  userId: number,
  amount: number,
  description: string,
  type: string = 'wallet_transfer',
  referenceId?: string,
  plaidEventId?: string,
  plaidTransferId?: string,
  metaData?: any,
  statusId: number = 1
): Promise<{ success: boolean; transactionId?: number; message?: string }> {
  try {
    // Create the wallet transaction
    const result = await executeUpdate(
      `INSERT INTO tbl_wallet_transactions
       (user_id, type, amount, reference_id, plaid_event_id, plaid_transfer_id, 
        payment_provider, description, status_id, created_at, meta_data)
       VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, NOW(), ?)`,
      [
        userId,
        type,
        amount,
        referenceId || `TXN_${Date.now()}_${userId}`,
        plaidEventId || null,
        plaidTransferId || null,
        type,
        description,
        statusId,
        metaData ? JSON.stringify(metaData) : null
      ]
    );

    const transactionId = result.insertId;

    // If we have Plaid event and transfer IDs, create tracking record
    if (plaidEventId && plaidTransferId) {
      const transferType = amount > 0 ? 'credit' : 'debit';
      await createTransferTracking(
        plaidTransferId,
        plaidEventId,
        userId,
        transferType,
        Math.abs(amount),
        statusId === 1 ? 'completed' : (statusId === 2 ? 'pending' : 'failed'),
        transactionId
      );

      // Record the event correlation
      await recordEventCorrelation(
        plaidTransferId,
        plaidEventId,
        'transaction_created',
        statusId === 1 ? 'completed' : (statusId === 2 ? 'pending' : 'failed'),
        {
          transactionId,
          userId,
          amount,
          type,
          description
        }
      );
    }

    logger.info('Enhanced wallet transaction created with event tracking', {
      transactionId,
      userId,
      amount,
      type,
      plaidEventId,
      plaidTransferId
    });

    return {
      success: true,
      transactionId,
      message: 'Wallet transaction created successfully with event tracking'
    };
  } catch (error) {
    logger.error('Error creating wallet transaction with event tracking', {
      userId,
      amount,
      type,
      plaidEventId,
      plaidTransferId,
      error: error instanceof Error ? error.message : 'Unknown error'
    });

    return {
      success: false,
      message: 'Failed to create wallet transaction with event tracking'
    };
  }
}

/**
 * Enhanced pending deposit creation with event ID tracking
 */
export async function createPendingDepositWithEventTracking(
  userId: number,
  amount: number,
  plaidTransferId: string,
  plaidEventId: string,
  description: string,
  metadata: any = {}
): Promise<{ success: boolean; holdId?: number; transactionId?: number; message?: string }> {
  try {
    // Create the pending hold record
    const holdResult = await executeUpdate(
      `INSERT INTO tbl_pending_holds 
       (user_id, amount, type, reference_id, plaid_transfer_id, plaid_event_id, 
        status, description, meta_data, created_at, expires_at)
       VALUES (?, ?, 'deposit', ?, ?, ?, 'pending', ?, ?, NOW(), DATE_ADD(NOW(), INTERVAL 7 DAY))`,
      [
        userId,
        amount,
        plaidTransferId,
        plaidTransferId,
        plaidEventId,
        description,
        JSON.stringify(metadata)
      ]
    );

    const holdId = holdResult.insertId;

    // Create wallet transaction with event tracking
    const transactionResult = await createWalletTransactionWithEventTracking(
      userId,
      amount,
      description,
      'deposit',
      plaidTransferId,
      plaidEventId,
      plaidTransferId,
      {
        ...metadata,
        holdId,
        status: 'pending'
      },
      2 // pending status
    );

    // Create transfer tracking record
    await createTransferTracking(
      plaidTransferId,
      plaidEventId,
      userId,
      'credit',
      amount,
      'pending',
      transactionResult.transactionId,
      holdId
    );

    // Record event correlation
    await recordEventCorrelation(
      plaidTransferId,
      plaidEventId,
      'deposit_pending_created',
      'pending',
      {
        holdId,
        transactionId: transactionResult.transactionId,
        userId,
        amount,
        description,
        metadata
      }
    );

    logger.info('Enhanced pending deposit created with event tracking', {
      userId,
      amount,
      plaidTransferId,
      plaidEventId,
      holdId,
      transactionId: transactionResult.transactionId
    });

    return {
      success: true,
      holdId,
      transactionId: transactionResult.transactionId,
      message: 'Pending deposit created successfully with event tracking'
    };
  } catch (error) {
    logger.error('Error creating pending deposit with event tracking', {
      userId,
      amount,
      plaidTransferId,
      plaidEventId,
      error: error instanceof Error ? error.message : 'Unknown error'
    });

    return {
      success: false,
      message: 'Failed to create pending deposit with event tracking'
    };
  }
}

/**
 * Enhanced withdrawal hold creation with event ID tracking
 */
export async function createWithdrawalHoldWithEventTracking(
  userId: number,
  amount: number,
  plaidTransferId: string,
  plaidEventId: string,
  description: string,
  metadata: any = {}
): Promise<{ success: boolean; holdId?: number; transactionId?: number; message?: string }> {
  try {
    // Create the pending hold record
    const holdResult = await executeUpdate(
      `INSERT INTO tbl_pending_holds 
       (user_id, amount, type, reference_id, plaid_transfer_id, plaid_event_id, 
        status, description, meta_data, created_at, expires_at)
       VALUES (?, ?, 'withdrawal', ?, ?, ?, 'pending', ?, ?, NOW(), DATE_ADD(NOW(), INTERVAL 7 DAY))`,
      [
        userId,
        amount,
        plaidTransferId,
        plaidTransferId,
        plaidEventId,
        description,
        JSON.stringify(metadata)
      ]
    );

    const holdId = holdResult.insertId;

    // Block the amount in wallet (increase blocked_balance)
    await executeUpdate(
      `UPDATE tbl_masterwallet 
       SET blocked_balance = blocked_balance + ? 
       WHERE user_id = ?`,
      [amount, userId]
    );

    // Create wallet transaction with event tracking
    const transactionResult = await createWalletTransactionWithEventTracking(
      userId,
      -amount, // negative for withdrawal
      description,
      'withdrawal',
      plaidTransferId,
      plaidEventId,
      plaidTransferId,
      {
        ...metadata,
        holdId,
        status: 'pending'
      },
      2 // pending status
    );

    // Create transfer tracking record
    await createTransferTracking(
      plaidTransferId,
      plaidEventId,
      userId,
      'debit',
      amount,
      'pending',
      transactionResult.transactionId,
      holdId
    );

    // Record event correlation
    await recordEventCorrelation(
      plaidTransferId,
      plaidEventId,
      'withdrawal_hold_created',
      'pending',
      {
        holdId,
        transactionId: transactionResult.transactionId,
        userId,
        amount,
        description,
        metadata
      }
    );

    logger.info('Enhanced withdrawal hold created with event tracking', {
      userId,
      amount,
      plaidTransferId,
      plaidEventId,
      holdId,
      transactionId: transactionResult.transactionId
    });

    return {
      success: true,
      holdId,
      transactionId: transactionResult.transactionId,
      message: 'Withdrawal hold created successfully with event tracking'
    };
  } catch (error) {
    logger.error('Error creating withdrawal hold with event tracking', {
      userId,
      amount,
      plaidTransferId,
      plaidEventId,
      error: error instanceof Error ? error.message : 'Unknown error'
    });

    return {
      success: false,
      message: 'Failed to create withdrawal hold with event tracking'
    };
  }
}
