import nodemailer from 'nodemailer';
import logger from '../utils/logger';
import EmailTemplateService from './emailTemplateService';

// Email configuration from environment variables
const emailConfig = {
  host: process.env.EMAIL_HOST || "smtp.office365.com",
  service: process.env.EMAIL_SERVICE || "Outlook365",
  port: parseInt(process.env.EMAIL_PORT || "587"),
  secure: process.env.EMAIL_SECURE === "true",
  auth: {
    user: process.env.EMAIL_USER || "<EMAIL>",
    pass: process.env.EMAIL_PASSWORD || "",
  },
};

// Create transporter
const transporter = nodemailer.createTransport(emailConfig);

// Verify transporter configuration (skip in development mode unless forced)
const isDevelopment = process.env.NODE_ENV === 'development';
const forceEmailInDev = process.env.FORCE_EMAIL_IN_DEV === 'true';

if (!isDevelopment || forceEmailInDev) {
  transporter.verify((error) => {
    if (error) {
      logger.error('Email transporter configuration error:', error);
    } else {
      logger.info('Email transporter is ready to send emails');
    }
  });
} else {
  logger.info('Email transporter verification skipped in development mode');
}

export interface EmailOptions {
  to: string;
  subject: string;
  text?: string;
  html?: string;
}

/**
 * Send email using nodemailer
 * @param options - Email options
 * @returns Promise<boolean> - Success status
 */
export async function sendEmail(options: EmailOptions): Promise<boolean> {
  // Skip sending emails in development mode unless forced
  const isDevelopment = process.env.NODE_ENV === 'development';
  const forceEmailInDev = process.env.FORCE_EMAIL_IN_DEV === 'true';
  console.log(isDevelopment && !forceEmailInDev, 'isDevelopment && !forceEmailInDev')
  if (isDevelopment && !forceEmailInDev) {
    logger.info('Email sending skipped in development mode', {
      to: options.to,
      subject: options.subject,
      mode: 'development',
      message: 'Email would have been sent in production. Set FORCE_EMAIL_IN_DEV=true to override.'
    });
    return true; // Return true to simulate successful sending
  }

  try {
    const mailOptions = {
      from: emailConfig.auth.user,
      to: options.to,
      subject: options.subject,
      text: options.text,
      html: options.html,
    };

    const info = await transporter.sendMail(mailOptions);
    logger.info('Email sent successfully', {
      to: options.to,
      subject: options.subject,
      messageId: info.messageId,
    });

    return true;
  } catch (error) {
    logger.error('Error sending email', {
      to: options.to,
      subject: options.subject,
      error: error instanceof Error ? error.message : 'Unknown error',
    });
    return false;
  }
}

/**
 * Send OTP email using template
 * @param email - Recipient email
 * @param otpCode - OTP code
 * @param otpType - Type of OTP
 * @returns Promise<boolean> - Success status
 */
export async function sendOTPEmail(
  email: string,
  otpCode: string,
  otpType: string = 'login'
): Promise<boolean> {
  try {
    const subject = getOTPEmailSubject(otpType);
    const action = getActionText(otpType);

    // Determine template based on OTP type
    const templateName = otpType === 'password_reset' ? 'password-reset' : 'otp-verification';

    // Load and process both HTML and text templates
    const { html, text } = await EmailTemplateService.loadAndProcessBothTemplates(templateName, {
      OTP_CODE: otpCode,
      ACTION: action,
    });

    return await sendEmail({
      to: email,
      subject,
      text,
      html,
    });
  } catch (error) {
    logger.error('Error sending OTP email with template', {
      email,
      otpType,
      error: error instanceof Error ? error.message : 'Unknown error',
    });

    // Fallback to simple content if template fails
    const subject = getOTPEmailSubject(otpType);
    const action = getActionText(otpType);

    // Simple fallback without complex HTML
    const text = `Your Pay Connect verification code for ${action} is: ${otpCode}\n\nThis code expires in 5 minutes.\n\nBest regards,\nPay Connect Team`;

    return await sendEmail({
      to: email,
      subject,
      text,
    });
  }
}

/**
 * Send welcome email using template
 * @param email - Recipient email
 * @param userName - User's name
 * @param dashboardUrl - URL to dashboard
 * @returns Promise<boolean> - Success status
 */
export async function sendWelcomeEmail(
  email: string,
  userName: string,
  dashboardUrl: string = process.env.FRONTEND_URL || 'http://localhost:5174'
): Promise<boolean> {
  try {
    const subject = 'Welcome to Pay Connect - Your Digital Wallet Awaits!';

    // Load and process both HTML and text templates
    const { html, text } = await EmailTemplateService.loadAndProcessBothTemplates('welcome', {
      USER_NAME: userName,
      DASHBOARD_URL: dashboardUrl,
    });

    return await sendEmail({
      to: email,
      subject,
      text,
      html,
    });
  } catch (error) {
    logger.error('Error sending welcome email with template', {
      email,
      userName,
      error: error instanceof Error ? error.message : 'Unknown error',
    });

    // Fallback to simple text email if template fails
    const subject = 'Welcome to Pay Connect!';
    const text = `Hello ${userName},\n\nWelcome to Pay Connect! Your account has been successfully created and you're ready to start using our digital wallet services.\n\nGet started: ${dashboardUrl}\n\nBest regards,\nPay Connect Team`;

    return await sendEmail({
      to: email,
      subject,
      text,
    });
  }
}

/**
 * Send transaction notification email
 * @param email - Recipient email
 * @param transactionData - Transaction details
 * @returns Promise<boolean> - Success status
 */
export async function sendTransactionNotificationEmail(
  email: string,
  transactionData: {
    id: string;
    type: 'credit' | 'debit';
    amount: string;
    description: string;
    date: string;
    currentBalance: string;
  }
): Promise<boolean> {
  try {
    const isCredit = transactionData.type === 'credit';
    const subject = `Pay Connect - ${isCredit ? 'Money Received' : 'Payment Made'} - ${transactionData.amount}`;

    // Load and process transaction notification template
    const { html, text } = await EmailTemplateService.loadAndProcessBothTemplates('transaction-notification', {
      TRANSACTION_ID: transactionData.id,
      TRANSACTION_TYPE: isCredit ? 'Completed' : 'Processed',
      TRANSACTION_CLASS: transactionData.type,
      TRANSACTION_DATE: transactionData.date,
      TRANSACTION_DESCRIPTION: transactionData.description,
      TRANSACTION_AMOUNT: transactionData.amount,
      AMOUNT_LABEL: isCredit ? 'Amount Received' : 'Amount Paid',
      CURRENT_BALANCE: transactionData.currentBalance,
      TRANSACTION_MESSAGE: isCredit
        ? 'Your wallet has been credited successfully.'
        : 'Your payment has been processed successfully.',
    });

    return await sendEmail({
      to: email,
      subject,
      text,
      html,
    });
  } catch (error) {
    logger.error('Error sending transaction notification email', {
      email,
      transactionId: transactionData.id,
      error: error instanceof Error ? error.message : 'Unknown error',
    });
    return false;
  }
}

/**
 * Send email using custom template
 * @param email - Recipient email
 * @param templateName - Name of the template file
 * @param variables - Template variables
 * @param subject - Email subject
 * @returns Promise<boolean> - Success status
 */
export async function sendTemplateEmail(
  email: string,
  templateName: string,
  variables: Record<string, string>,
  subject: string
): Promise<boolean> {
  try {
    // Load and process both HTML and text templates
    const { html, text } = await EmailTemplateService.loadAndProcessBothTemplates(templateName, variables);

    return await sendEmail({
      to: email,
      subject,
      text,
      html,
    });
  } catch (error) {
    logger.error('Error sending template email', {
      email,
      templateName,
      subject,
      error: error instanceof Error ? error.message : 'Unknown error',
    });
    return false;
  }
}

/**
 * Get available email templates
 * @returns Promise<string[]> - Array of available template names
 */
export async function getAvailableEmailTemplates(): Promise<string[]> {
  return await EmailTemplateService.getAvailableTemplates();
}

/**
 * Get OTP email subject based on type
 * @param otpType - Type of OTP
 * @returns string - Email subject
 */
function getOTPEmailSubject(otpType: string): string {
  switch (otpType) {
    case 'login':
      return 'Your Pay Connect Login Verification Code';
    case 'transaction':
      return 'Your Pay Connect Transaction Verification Code';
    case 'password_reset':
      return 'Your Pay Connect Password Reset Code';
    case 'email_verify':
      return 'Verify Your Pay Connect Email Address';
    case 'phone_verify':
      return 'Verify Your Pay Connect Phone Number';
    default:
      return 'Your Pay Connect Verification Code';
  }
}
export async function sendReminderEmail(
  user: any,
  dashboardUrl: string = process.env.FRONTEND_URL || 'http://localhost:5174'
): Promise<boolean> {
  try {
    let subject = '';
    let html = '';
    let text = '';
    console.log(user.players, 'user.players ')


    if (user.role_name === 'player') {
      subject = 'Payment Reminder: Complete Your Pay Connect Registration!';

      const templates = await EmailTemplateService.loadAndProcessBothTemplates('player-reminder', {
        PLAYER_NAME: user.firstname,
        LAST_PAYMENT_DATE: user.final_day_of_payment,
      });

      html = templates.html;
      text = templates.text;
    } else {
      subject = 'Players with Pending Payments – Pay Connect Summary!';

      // Build HTML list of players
const playerListHtml =
  Array.isArray(user.players) && user.players.length > 0
    ? `
<table>
  <thead>
    <tr>
      <th>#</th>
      <th>Player Name</th>
      <th>Email</th>
      <th>Last Payment</th>
      <th>Amount Charged</th>
    </tr>
  </thead>
  <tbody>
    ${user.players
      .map((player: any, index: number) => {
        const number = index + 1;
        const name = player.firstname || 'N/A';
        const email = player.email || 'N/A';
        const paymentDate = player.final_day_of_payment || 'N/A';
        const amount = player.player_charged_amount ? `$${player.player_charged_amount}` : 'N/A';

        return `
          <tr>
            <td>${number}</td>
            <td>${name}</td>
            <td>${email}</td>
            <td>${paymentDate}</td>
            <td>${amount}</td>
          </tr>
        `;
      })
      .join('')}
  </tbody>
</table>
`
    : `<p><strong>No pending players found.</strong></p>`;

      const templates = await EmailTemplateService.loadAndProcessBothTemplates('team-head-reminder', {
        PLAYER_LIST: playerListHtml,
        ADMIN_NAME: user.firstname,
        YEAR: new Date().getFullYear().toString(),
      });


      html = templates.html;
      text = templates.text;
    }

    return await sendEmail({
      to: user.email,
      subject,
      text,
      html,
    });
  } catch (error) {
    logger.error('Error sending reminder email with template', {
      email: user.email,
      error: error instanceof Error ? error.message : 'Unknown error',
    });

    // fallback to plain-text if templates failed
    return await sendEmail({
      to: user.email,
      subject: 'Payment Reminder',
      text: 'Please complete your payment or review pending players on Pay Connect.',
    });
  }
}

/**
 * Get action text based on OTP type
 * @param otpType - Type of OTP
 * @returns string - Action description
 */
function getActionText(otpType: string): string {
  switch (otpType) {
    case 'login':
      return 'logging into your account';
    case 'transaction':
      return 'completing your transaction';
    case 'password_reset':
      return 'resetting your password';
    case 'email_verify':
      return 'verifying your email address';
    case 'phone_verify':
      return 'verifying your phone number';
    default:
      return 'account verification';
  }
}

export default transporter;
