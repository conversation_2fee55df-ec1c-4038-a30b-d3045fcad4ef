import { plaidClient } from '../config/plaidConfig';
import logger from '../utils/logger';
import {
  TransferEventType,
  TransferGetRequest
} from 'plaid';
import { updateTransferStatus } from './plaidTransferService';

export interface SandboxTransferControl {
  success: boolean;
  message: string;
  transferId?: string;
  status?: string;
  webhookFired?: boolean;
}

export interface TransferSimulationOptions {
  transferId: string;
  eventType: 'success' | 'failure' | 'return' | 'cancel';
  failureReason?: string;
  customFailureReason?: string;
  delay?: number; // Delay in milliseconds before firing webhook
}

/**
 * Plaid Sandbox Manager for manually controlling transfer outcomes
 * This service provides functions to simulate various transfer scenarios in sandbox environment
 */
export class PlaidSandboxManager {
  
  /**
   * Check if we're in sandbox environment
   */
  private static isSandbox(): boolean {
    return process.env.PLAID_ENV === 'sandbox';
  }

  /**
   * Manually mark a transfer as successful
   */
  static async markTransferAsSuccessful(transferId: string, delay: number = 0): Promise<SandboxTransferControl> {
    if (!this.isSandbox()) {
      return {
        success: false,
        message: 'Transfer simulation is only available in sandbox environment'
      };
    }

    try {
      logger.info('Manually marking transfer as successful', { transferId, delay });

      // Add delay if specified
      if (delay > 0) {
        await new Promise(resolve => setTimeout(resolve, delay));
      }

      // Fire webhook to simulate transfer success
      await plaidClient.sandboxTransferFireWebhook({
        webhook_code: 'TRANSFER_EVENTS_UPDATE'
      } as any);

      // Update local status to 'posted' (successful)
      await updateTransferStatus(transferId, 'posted');

      logger.info('Transfer marked as successful', { transferId });

      return {
        success: true,
        message: 'Transfer marked as successful',
        transferId,
        status: 'posted',
        webhookFired: true
      };

    } catch (error: any) {
      logger.error('Error marking transfer as successful', { 
        transferId, 
        error: error.message 
      });
      
      return {
        success: false,
        message: `Failed to mark transfer as successful: ${error.message}`,
        transferId
      };
    }
  }

  /**
   * Manually mark a transfer as failed
   */
  static async markTransferAsFailed(
    transferId: string,
    failureReason: string = 'insufficient_funds',
    customReason?: string,
    delay: number = 0
  ): Promise<SandboxTransferControl> {
    if (!this.isSandbox()) {
      return {
        success: false,
        message: 'Transfer simulation is only available in sandbox environment'
      };
    }

    try {
      logger.info('Manually marking transfer as failed', { 
        transferId, 
        failureReason, 
        customReason,
        delay 
      });

      // Add delay if specified
      if (delay > 0) {
        await new Promise(resolve => setTimeout(resolve, delay));
      }

      // Fire webhook to simulate transfer failure
      await plaidClient.sandboxTransferFireWebhook({
        webhook_code: 'TRANSFER_FAILED'
      } as any);

      // Update local status to 'failed'
      const reasonText = customReason || failureReason;
      await updateTransferStatus(transferId, 'failed', reasonText);

      logger.info('Transfer marked as failed', { transferId, failureReason: reasonText });

      return {
        success: true,
        message: `Transfer marked as failed: ${reasonText}`,
        transferId,
        status: 'failed',
        webhookFired: true
      };

    } catch (error: any) {
      logger.error('Error marking transfer as failed', { 
        transferId, 
        error: error.message 
      });
      
      return {
        success: false,
        message: `Failed to mark transfer as failed: ${error.message}`,
        transferId
      };
    }
  }

  /**
   * Manually mark a transfer as returned
   */
  static async markTransferAsReturned(
    transferId: string,
    returnReason: string = 'Account closed',
    delay: number = 0
  ): Promise<SandboxTransferControl> {
    if (!this.isSandbox()) {
      return {
        success: false,
        message: 'Transfer simulation is only available in sandbox environment'
      };
    }

    try {
      logger.info('Manually marking transfer as returned', { 
        transferId, 
        returnReason,
        delay 
      });

      // Add delay if specified
      if (delay > 0) {
        await new Promise(resolve => setTimeout(resolve, delay));
      }

      // Fire webhook to simulate transfer return
      await plaidClient.sandboxTransferFireWebhook({
        webhook_code: 'TRANSFER_RETURNED'
      } as any);

      // Update local status to 'returned'
      await updateTransferStatus(transferId, 'returned', returnReason);

      logger.info('Transfer marked as returned', { transferId, returnReason });

      return {
        success: true,
        message: `Transfer marked as returned: ${returnReason}`,
        transferId,
        status: 'returned',
        webhookFired: true
      };

    } catch (error: any) {
      logger.error('Error marking transfer as returned', { 
        transferId, 
        error: error.message 
      });
      
      return {
        success: false,
        message: `Failed to mark transfer as returned: ${error.message}`,
        transferId
      };
    }
  }

  /**
   * Manually cancel a transfer
   */
  static async cancelTransfer(transferId: string, delay: number = 0): Promise<SandboxTransferControl> {
    if (!this.isSandbox()) {
      return {
        success: false,
        message: 'Transfer simulation is only available in sandbox environment'
      };
    }

    try {
      logger.info('Manually cancelling transfer', { transferId, delay });

      // Add delay if specified
      if (delay > 0) {
        await new Promise(resolve => setTimeout(resolve, delay));
      }

      // Fire webhook to simulate transfer cancellation
      await plaidClient.sandboxTransferFireWebhook({
        webhook_code: 'TRANSFER_CANCELLED'
      } as any);

      // Update local status to 'cancelled'
      await updateTransferStatus(transferId, 'cancelled', 'Transfer cancelled manually');

      logger.info('Transfer cancelled', { transferId });

      return {
        success: true,
        message: 'Transfer cancelled successfully',
        transferId,
        status: 'cancelled',
        webhookFired: true
      };

    } catch (error: any) {
      logger.error('Error cancelling transfer', {
        transferId,
        error: error.message
      });

      return {
        success: false,
        message: `Failed to cancel transfer: ${error.message}`,
        transferId
      };
    }
  }

  /**
   * Simulate a transfer with specific outcome
   */
  static async simulateTransfer(options: TransferSimulationOptions): Promise<SandboxTransferControl> {
    const { transferId, eventType, failureReason, customFailureReason, delay = 0 } = options;

    switch (eventType) {
      case 'success':
        return this.markTransferAsSuccessful(transferId, delay);

      case 'failure':
        return this.markTransferAsFailed(
          transferId,
          failureReason || 'insufficient_funds',
          customFailureReason,
          delay
        );

      case 'return':
        return this.markTransferAsReturned(
          transferId,
          customFailureReason || 'Account closed',
          delay
        );

      case 'cancel':
        return this.cancelTransfer(transferId, delay);

      default:
        return {
          success: false,
          message: `Invalid event type: ${eventType}. Must be 'success', 'failure', 'return', or 'cancel'`
        };
    }
  }

  /**
   * Get current transfer status from Plaid
   */
  static async getTransferStatus(transferId: string): Promise<{
    success: boolean;
    status?: string;
    failureReason?: string;
    message?: string;
  }> {
    try {
      const request: TransferGetRequest = {
        transfer_id: transferId
      };

      const response = await plaidClient.transferGet(request);
      const transfer = response.data.transfer;

      return {
        success: true,
        status: transfer.status,
        failureReason: transfer.failure_reason?.toString() || undefined
      };

    } catch (error: any) {
      logger.error('Error getting transfer status', {
        transferId,
        error: error.message
      });

      return {
        success: false,
        message: `Failed to get transfer status: ${error.message}`
      };
    }
  }

  /**
   * List all available failure reasons for testing
   */
  static getAvailableFailureReasons(): { value: string; description: string }[] {
    return [
      { value: 'insufficient_funds', description: 'Insufficient funds in account' },
      { value: 'account_closed', description: 'Bank account is closed' },
      { value: 'debit_not_authorized', description: 'Debit not authorized by account holder' },
      { value: 'invalid_account_number', description: 'Invalid account number' },
      { value: 'invalid_routing_number', description: 'Invalid routing number' },
      { value: 'unauthorized_environment', description: 'Unauthorized environment' },
      { value: 'transaction_not_permitted', description: 'Transaction not permitted' },
      { value: 'generic_error', description: 'Generic error occurred' }
    ];
  }

  /**
   * Batch simulate multiple transfers
   */
  static async batchSimulateTransfers(
    simulations: TransferSimulationOptions[]
  ): Promise<SandboxTransferControl[]> {
    if (!this.isSandbox()) {
      return simulations.map(() => ({
        success: false,
        message: 'Transfer simulation is only available in sandbox environment'
      }));
    }

    const results: SandboxTransferControl[] = [];

    for (const simulation of simulations) {
      try {
        const result = await this.simulateTransfer(simulation);
        results.push(result);

        // Add a small delay between batch operations to avoid rate limiting
        await new Promise(resolve => setTimeout(resolve, 100));

      } catch (error: any) {
        results.push({
          success: false,
          message: `Batch simulation failed for ${simulation.transferId}: ${error.message}`,
          transferId: simulation.transferId
        });
      }
    }

    return results;
  }
}
