import dotenv from 'dotenv';
dotenv.config();

const API_BASE_URL = process.env.API_BASE_URL || 'http://localhost:8000/apis/payconnect';

export async function fetchFromApi(endpoint: string, method: 'GET' | 'POST' = 'GET', body?: any) {
  
  const url = `${API_BASE_URL}${endpoint}`;

  console.log(url,'uurururururu')
  try {
    const response = await fetch(url, {
      method,
      headers: { 'Content-Type': 'application/json' },
      body: body ? JSON.stringify(body) : undefined,
    });

    const data = await response.json(); // parse JSON instead of text

    return data;
  } catch (error) {
    console.error('fetchFromApi error:', error instanceof Error ? error.message : error);
    throw error;
  }
}
