import { executeUpdate, executeQuerySingle, executeQuery } from '../utils/database';
import logger from '../utils/logger';

export interface PlaidTransferTracking {
  id: number;
  plaid_transfer_id: string;
  plaid_event_id: string;
  user_id: number;
  transfer_type: 'debit' | 'credit';
  amount: number;
  status: string;
  failure_reason?: string;
  wallet_transaction_id?: number;
  pending_hold_id?: number;
  created_at: Date;
  updated_at: Date;
}

export interface PlaidEventCorrelation {
  id: number;
  plaid_transfer_id: string;
  plaid_event_id: string;
  event_type: string;
  event_status: string;
  webhook_received_at: Date;
  processed_at?: Date;
  processing_status: 'pending' | 'processed' | 'failed' | 'ignored';
  error_message?: string;
  raw_webhook_data: any;
}

/**
 * Create a new transfer tracking record with event ID
 */
export async function createTransferTracking(
  plaidTransferId: string,
  plaidEventId: string,
  userId: number,
  transferType: 'debit' | 'credit',
  amount: number,
  status: string,
  walletTransactionId?: number,
  pendingHoldId?: number
): Promise<{ success: boolean; trackingId?: number; message?: string }> {
  try {
    const result = await executeUpdate(
      `INSERT INTO tbl_plaid_transfer_tracking 
       (plaid_transfer_id, plaid_event_id, user_id, transfer_type, amount, status, 
        wallet_transaction_id, pending_hold_id, created_at, updated_at)
       VALUES (?, ?, ?, ?, ?, ?, ?, ?, NOW(), NOW())`,
      [
        plaidTransferId,
        plaidEventId,
        userId,
        transferType,
        amount,
        status,
        walletTransactionId || null,
        pendingHoldId || null
      ]
    );

    logger.info('Transfer tracking record created', {
      plaidTransferId,
      plaidEventId,
      userId,
      trackingId: result.insertId
    });

    return {
      success: true,
      trackingId: result.insertId,
      message: 'Transfer tracking record created successfully'
    };
  } catch (error) {
    logger.error('Error creating transfer tracking record', {
      plaidTransferId,
      plaidEventId,
      userId,
      error: error instanceof Error ? error.message : 'Unknown error'
    });

    return {
      success: false,
      message: 'Failed to create transfer tracking record'
    };
  }
}

/**
 * Update transfer tracking status with event ID
 */
export async function updateTransferTrackingStatus(
  plaidTransferId: string,
  plaidEventId: string,
  status: string,
  failureReason?: string
): Promise<{ success: boolean; message?: string }> {
  try {
    const result = await executeUpdate(
      `UPDATE tbl_plaid_transfer_tracking 
       SET status = ?, failure_reason = ?, updated_at = NOW()
       WHERE plaid_transfer_id = ?`,
      [status, failureReason || null, plaidTransferId]
    );

    // Also record the event correlation
    await recordEventCorrelation(
      plaidTransferId,
      plaidEventId,
      'status_update',
      status,
      { status, failure_reason: failureReason }
    );

    logger.info('Transfer tracking status updated', {
      plaidTransferId,
      plaidEventId,
      status,
      affectedRows: result.affectedRows
    });

    return {
      success: true,
      message: 'Transfer tracking status updated successfully'
    };
  } catch (error) {
    logger.error('Error updating transfer tracking status', {
      plaidTransferId,
      plaidEventId,
      status,
      error: error instanceof Error ? error.message : 'Unknown error'
    });

    return {
      success: false,
      message: 'Failed to update transfer tracking status'
    };
  }
}

/**
 * Record event correlation for tracking event sequences
 */
export async function recordEventCorrelation(
  plaidTransferId: string,
  plaidEventId: string,
  eventType: string,
  eventStatus: string,
  rawWebhookData: any,
  processingStatus: 'pending' | 'processed' | 'failed' | 'ignored' = 'pending'
): Promise<{ success: boolean; correlationId?: number; message?: string }> {
  try {
    const result = await executeUpdate(
      `INSERT INTO tbl_plaid_event_correlation 
       (plaid_transfer_id, plaid_event_id, event_type, event_status, 
        webhook_received_at, processing_status, raw_webhook_data)
       VALUES (?, ?, ?, ?, NOW(), ?, ?)
       ON DUPLICATE KEY UPDATE
       event_status = VALUES(event_status),
       processing_status = VALUES(processing_status),
       raw_webhook_data = VALUES(raw_webhook_data)`,
      [
        plaidTransferId,
        plaidEventId,
        eventType,
        eventStatus,
        processingStatus,
        JSON.stringify(rawWebhookData)
      ]
    );

    logger.info('Event correlation recorded', {
      plaidTransferId,
      plaidEventId,
      eventType,
      eventStatus,
      correlationId: result.insertId
    });

    return {
      success: true,
      correlationId: result.insertId,
      message: 'Event correlation recorded successfully'
    };
  } catch (error) {
    logger.error('Error recording event correlation', {
      plaidTransferId,
      plaidEventId,
      eventType,
      error: error instanceof Error ? error.message : 'Unknown error'
    });

    return {
      success: false,
      message: 'Failed to record event correlation'
    };
  }
}

/**
 * Mark event correlation as processed
 */
export async function markEventAsProcessed(
  plaidEventId: string,
  errorMessage?: string
): Promise<{ success: boolean; message?: string }> {
  try {
    const processingStatus = errorMessage ? 'failed' : 'processed';
    
    const result = await executeUpdate(
      `UPDATE tbl_plaid_event_correlation 
       SET processing_status = ?, processed_at = NOW(), error_message = ?
       WHERE plaid_event_id = ?`,
      [processingStatus, errorMessage || null, plaidEventId]
    );

    logger.info('Event marked as processed', {
      plaidEventId,
      processingStatus,
      errorMessage,
      affectedRows: result.affectedRows
    });

    return {
      success: true,
      message: 'Event marked as processed successfully'
    };
  } catch (error) {
    logger.error('Error marking event as processed', {
      plaidEventId,
      error: error instanceof Error ? error.message : 'Unknown error'
    });

    return {
      success: false,
      message: 'Failed to mark event as processed'
    };
  }
}

/**
 * Get transfer tracking by transfer ID
 */
export async function getTransferTracking(
  plaidTransferId: string
): Promise<PlaidTransferTracking | null> {
  try {
    const tracking = await executeQuerySingle<PlaidTransferTracking>(
      `SELECT * FROM tbl_plaid_transfer_tracking 
       WHERE plaid_transfer_id = ?`,
      [plaidTransferId]
    );

    return tracking;
  } catch (error) {
    logger.error('Error getting transfer tracking', {
      plaidTransferId,
      error: error instanceof Error ? error.message : 'Unknown error'
    });
    return null;
  }
}

/**
 * Get event correlations for a transfer
 */
export async function getEventCorrelations(
  plaidTransferId: string
): Promise<PlaidEventCorrelation[]> {
  try {
    const correlations = await executeQuery<PlaidEventCorrelation>(
      `SELECT * FROM tbl_plaid_event_correlation 
       WHERE plaid_transfer_id = ?
       ORDER BY webhook_received_at DESC`,
      [plaidTransferId]
    );

    return Array.isArray(correlations) ? correlations : (correlations ? [correlations] : []);
  } catch (error) {
    logger.error('Error getting event correlations', {
      plaidTransferId,
      error: error instanceof Error ? error.message : 'Unknown error'
    });
    return [];
  }
}

/**
 * Check if event has already been processed
 */
export async function isEventAlreadyProcessed(
  plaidEventId: string
): Promise<boolean> {
  try {
    const correlation = await executeQuerySingle(
      `SELECT id FROM tbl_plaid_event_correlation 
       WHERE plaid_event_id = ? AND processing_status IN ('processed', 'ignored')`,
      [plaidEventId]
    );

    return !!correlation;
  } catch (error) {
    logger.error('Error checking if event is already processed', {
      plaidEventId,
      error: error instanceof Error ? error.message : 'Unknown error'
    });
    return false;
  }
}

/**
 * Get comprehensive transfer status with all related data
 */
export async function getComprehensiveTransferStatus(
  plaidTransferId: string
): Promise<any> {
  try {
    const status = await executeQuerySingle(
      `SELECT * FROM vw_transfer_status 
       WHERE plaid_transfer_id = ?`,
      [plaidTransferId]
    );

    return status;
  } catch (error) {
    logger.error('Error getting comprehensive transfer status', {
      plaidTransferId,
      error: error instanceof Error ? error.message : 'Unknown error'
    });
    return null;
  }
}

/**
 * Clean up old event correlations (older than 30 days)
 */
export async function cleanupOldEventCorrelations(): Promise<{ success: boolean; deletedCount?: number; message?: string }> {
  try {
    const result = await executeUpdate(
      `DELETE FROM tbl_plaid_event_correlation
       WHERE webhook_received_at < DATE_SUB(NOW(), INTERVAL 30 DAY)
       AND processing_status IN ('processed', 'ignored')`
    );

    logger.info('Old event correlations cleaned up', {
      deletedCount: result.affectedRows
    });

    return {
      success: true,
      deletedCount: result.affectedRows,
      message: `Cleaned up ${result.affectedRows} old event correlations`
    };
  } catch (error) {
    logger.error('Error cleaning up old event correlations', {
      error: error instanceof Error ? error.message : 'Unknown error'
    });

    return {
      success: false,
      message: 'Failed to clean up old event correlations'
    };
  }
}

/**
 * Update wallet transaction with event ID
 */
export async function updateWalletTransactionWithEventId(
  transactionId: number,
  plaidEventId: string,
  plaidTransferId: string
): Promise<{ success: boolean; message?: string }> {
  try {
    const result = await executeUpdate(
      `UPDATE tbl_wallet_transactions
       SET plaid_event_id = ?, plaid_transfer_id = ?
       WHERE id = ?`,
      [plaidEventId, plaidTransferId, transactionId]
    );

    logger.info('Wallet transaction updated with event ID', {
      transactionId,
      plaidEventId,
      plaidTransferId,
      affectedRows: result.affectedRows
    });

    return {
      success: true,
      message: 'Wallet transaction updated with event ID successfully'
    };
  } catch (error) {
    logger.error('Error updating wallet transaction with event ID', {
      transactionId,
      plaidEventId,
      plaidTransferId,
      error: error instanceof Error ? error.message : 'Unknown error'
    });

    return {
      success: false,
      message: 'Failed to update wallet transaction with event ID'
    };
  }
}

/**
 * Update pending hold with event ID
 */
export async function updatePendingHoldWithEventId(
  holdId: number,
  plaidEventId: string
): Promise<{ success: boolean; message?: string }> {
  try {
    const result = await executeUpdate(
      `UPDATE tbl_pending_holds
       SET plaid_event_id = ?
       WHERE id = ?`,
      [plaidEventId, holdId]
    );

    logger.info('Pending hold updated with event ID', {
      holdId,
      plaidEventId,
      affectedRows: result.affectedRows
    });

    return {
      success: true,
      message: 'Pending hold updated with event ID successfully'
    };
  } catch (error) {
    logger.error('Error updating pending hold with event ID', {
      holdId,
      plaidEventId,
      error: error instanceof Error ? error.message : 'Unknown error'
    });

    return {
      success: false,
      message: 'Failed to update pending hold with event ID'
    };
  }
}
