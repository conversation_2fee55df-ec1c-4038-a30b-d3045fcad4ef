import { executeQuery, executeQuery<PERSON>ingle, executeUpdate } from '../utils/database';
import logger from '../utils/logger';

export interface PaymentMethod {
  id: number;
  method_id: string;
  name: string;
  description: string;
  processing_time: string;
  fee_type: 'fixed' | 'percentage' | 'hybrid';
  percentage_fee?: number;
  min_fee?: number;
  max_fee?: number;
  is_active: boolean;
  method_type: 'withdrawal' | 'deposit' | 'both';
  min_amount: number;
  max_amount: number | null;
  display_order: number;
  created_at: string;
  updated_at: string;
}

export interface WithdrawalStep {
  id: number;
  step_key: string;
  title: string;
  description: string;
  step_order: number;
  is_active: boolean;
  created_at: string;
  updated_at: string;
}

export interface WithdrawalProgress {
  id: number;
  transaction_id: number;
  step_key: string;
  status: 'pending' | 'completed' | 'failed' | 'skipped';
  completed_at: string | null;
  notes: string | null;
  created_at: string;
  updated_at: string;
}

/**
 * Calculate the actual fee for a payment method based on amount
 */
export function calculatePaymentMethodFee(
  paymentMethod: PaymentMethod,
  amount: number
): number {
  try {
    // Parse fee values safely
    const minFee = parseFloat(paymentMethod.min_fee?.toString() || '0') || 0;
    const maxFee = parseFloat(paymentMethod.max_fee?.toString() || '0') || 0;
    const percentageFee = parseFloat(paymentMethod.percentage_fee?.toString() || '0') || 0;

    let calculatedFee = 0;

    // Calculate fee based on fee_type
    switch (paymentMethod.fee_type) {
      case 'fixed':
        // For fixed fees, use min_fee as the fixed amount (or 0 if not set)
        calculatedFee = minFee;
        break;

      case 'percentage':
        // Pure percentage fee with min/max constraints
        if (percentageFee > 0) {
          calculatedFee = (amount * percentageFee) / 100;
        }
        break;

      case 'hybrid':
        // Combination of base fee and percentage
        calculatedFee = minFee; // Base fee
        if (percentageFee > 0) {
          const percentageAmount = (amount * percentageFee) / 100;
          calculatedFee += percentageAmount;
        }
        break;

      default:
        calculatedFee = 0;
    }

    // Apply min/max fee constraints for percentage and hybrid types
    if (paymentMethod.fee_type !== 'fixed') {
      if (minFee > 0) {
        calculatedFee = Math.max(calculatedFee, minFee);
      }

      if (maxFee > 0) {
        calculatedFee = Math.min(calculatedFee, maxFee);
      }
    }

    return Math.round(calculatedFee * 100) / 100; // Round to 2 decimal places
  } catch (error) {
    logger.error('Error calculating payment method fee', { error, paymentMethod, amount });
    return 0;
  }
}

/**
 * Get all active payment methods
 */
export async function getPaymentMethods(methodType?: 'withdrawal' | 'deposit' | 'both'): Promise<PaymentMethod[]> {
  try {
    let query = `
      SELECT
        id, method_id, name, description, processing_time,
        fee_type,
        COALESCE(min_fee, 0) as min_fee,
        COALESCE(max_fee, 0) as max_fee,
        COALESCE(percentage_fee, 0) as percentage_fee,
        is_active, method_type, min_amount, max_amount, display_order,
        created_at, updated_at
      FROM tbl_payment_methods
      WHERE is_active = 1
    `;
    const params: any[] = [];

    if (methodType) {
      query += ` AND (method_type = ? OR method_type = 'both')`;
      params.push(methodType);
    }

    query += ` ORDER BY display_order ASC, name ASC`;

    const methods = await executeQuery(query, params);
    return Array.isArray(methods) ? methods as PaymentMethod[] : [];
  } catch (error) {
    logger.error('Error fetching payment methods', { error, methodType });
    return [];
  }
}

/**
 * Get payment method by ID
 */
export async function getPaymentMethodById(methodId: string): Promise<PaymentMethod | null> {
  try {
    const method = await executeQuerySingle(
      'SELECT * FROM tbl_payment_methods WHERE method_id = ? AND is_active = 1',
      [methodId]
    );
    return method as PaymentMethod | null;
  } catch (error) {
    logger.error('Error fetching payment method by ID', { error, methodId });
    return null;
  }
}

/**
 * Get all withdrawal steps
 */
export async function getWithdrawalSteps(): Promise<WithdrawalStep[]> {
  try {
    const steps = await executeQuery(
      'SELECT * FROM tbl_withdrawal_steps WHERE is_active = 1 ORDER BY step_order ASC'
    );
    return Array.isArray(steps) ? steps as WithdrawalStep[] : [];
  } catch (error) {
    logger.error('Error fetching withdrawal steps', { error });
    return [];
  }
}

/**
 * Initialize withdrawal progress for a transaction (only creates the initial step)
 */
export async function initializeWithdrawalProgress(transactionId: number): Promise<boolean> {
  return await initializeWithdrawal(transactionId);
}

/**
 * Add a new step to withdrawal progress
 */
export async function addWithdrawalStep(
  transactionId: number,
  stepKey: string,
  status: 'pending' | 'completed' | 'failed' | 'skipped' = 'completed',
  notes?: string
): Promise<boolean> {
  try {
    const completedAt = status === 'completed' ? new Date() : null;

    // First, verify that the step_key exists in tbl_withdrawal_steps
    const stepExists = await executeQuerySingle(
      'SELECT step_key FROM tbl_withdrawal_steps WHERE step_key = ? AND is_active = 1',
      [stepKey]
    );

    if (!stepExists) {
      logger.error('Invalid step key - step does not exist in withdrawal steps', {
        transactionId,
        stepKey,
        availableSteps: 'initiated, validated, processing, sent_to_bank, transferred, completed, failed_validation, failed_processing, rejected_by_bank, reversed, refunded, cancelled, under_review, review_approved, review_rejected'
      });
      return false;
    }

    // Check if step already exists
    const existingStep = await executeQuerySingle(
      'SELECT id FROM tbl_withdrawal_progress WHERE transaction_id = ? AND step_key = ?',
      [transactionId, stepKey]
    );

    if (existingStep) {
      // Update existing step
      await executeUpdate(
        `UPDATE tbl_withdrawal_progress
         SET status = ?, completed_at = ?, notes = ?, updated_at = CURRENT_TIMESTAMP
         WHERE transaction_id = ? AND step_key = ?`,
        [status, completedAt, notes || null, transactionId, stepKey]
      );
    } else {
      // Insert new step
      await executeUpdate(
        `INSERT INTO tbl_withdrawal_progress (transaction_id, step_key, status, completed_at, notes)
         VALUES (?, ?, ?, ?, ?)`,
        [transactionId, stepKey, status, completedAt, notes || null]
      );
    }

    logger.info('Withdrawal step added/updated', { transactionId, stepKey, status });
    return true;
  } catch (error) {
    logger.error('Error adding withdrawal step', {
      error: error instanceof Error ? error.message : 'Unknown error',
      errorStack: error instanceof Error ? error.stack : undefined,
      transactionId,
      stepKey,
      status,
      sqlState: (error as any)?.sqlState,
      sqlMessage: (error as any)?.sqlMessage
    });
    return false;
  }
}

/**
 * Update withdrawal step status (legacy function for backward compatibility)
 */
export async function updateWithdrawalStepStatus(
  transactionId: number,
  stepKey: string,
  status: 'pending' | 'completed' | 'failed' | 'skipped',
  notes?: string
): Promise<boolean> {
  return addWithdrawalStep(transactionId, stepKey, status, notes);
}

/**
 * Get withdrawal progress for a transaction (only actual steps that occurred)
 */
export async function getWithdrawalProgress(transactionId: number): Promise<{
  step_key: string;
  title: string;
  description: string;
  step_order: number;
  status: string;
  completed_at: string | null;
  notes: string | null;
}[]> {
  try {
    const progress = await executeQuery(
      `SELECT
        wp.step_key,
        ws.title,
        ws.description,
        ws.step_order,
        wp.status,
        wp.completed_at,
        wp.notes
       FROM tbl_withdrawal_progress wp
       JOIN tbl_withdrawal_steps ws ON wp.step_key = ws.step_key
       WHERE wp.transaction_id = ? AND ws.is_active = 1
       ORDER BY wp.created_at ASC`,
      [transactionId]
    );

    return Array.isArray(progress) ? progress : [];
  } catch (error) {
    logger.error('Error fetching withdrawal progress', { error, transactionId });
    return [];
  }
}

/**
 * Get withdrawal progress with transaction details
 */
export async function getWithdrawalProgressWithTransaction(transactionId: number): Promise<{
  transaction: any;
  steps: any[];
} | null> {
  try {
    // Get transaction details
    const transaction = await executeQuerySingle(
      'SELECT * FROM tbl_wallet_transactions WHERE id = ?',
      [transactionId]
    );

    if (!transaction) {
      return null;
    }

    // Get progress steps
    const steps = await getWithdrawalProgress(transactionId);

    return {
      transaction,
      steps
    };
  } catch (error) {
    logger.error('Error fetching withdrawal progress with transaction', { error, transactionId });
    return null;
  }
}

/**
 * Progress withdrawal to next step
 */
export async function progressWithdrawalToNextStep(
  transactionId: number,
  nextStepKey: string,
  status: 'pending' | 'completed' | 'failed' = 'completed',
  notes?: string
): Promise<boolean> {
  try {
    await addWithdrawalStep(transactionId, nextStepKey, status, notes);
    logger.info('Withdrawal progressed to next step', { transactionId, nextStepKey, status });
    return true;
  } catch (error) {
    logger.error('Error progressing withdrawal step', { error, transactionId, nextStepKey });
    return false;
  }
}

/**
 * Initialize withdrawal with first step and start processing
 */
export async function initializeWithdrawal(transactionId: number): Promise<boolean> {
  try {
    // Step 1: Mark as initiated
    await progressWithdrawalToNextStep(transactionId, 'initiated', 'completed', 'Withdrawal request received and initiated');
    logger.info('Withdrawal initialized', { transactionId });

    // Start the processing flow asynchronously
    setImmediate(() => processWithdrawalFlow(transactionId));

    return true;
  } catch (error) {
    logger.error('Error initializing withdrawal', { error, transactionId });
    return false;
  }
}

/**
 * Process withdrawal flow with realistic timing
 */
async function processWithdrawalFlow(transactionId: number): Promise<void> {
  try {
    logger.info('Starting withdrawal processing flow', { transactionId });

    // Get transaction details to determine payment method
    const transaction = await executeQuerySingle(
      'SELECT * FROM tbl_wallet_transactions WHERE id = ?',
      [transactionId]
    );

    if (!transaction) {
      logger.error('Transaction not found for processing', { transactionId });
      return;
    }

    const metaData = typeof transaction.meta_data === 'string'
      ? JSON.parse(transaction.meta_data)
      : transaction.meta_data;

    const paymentMethod = metaData?.paymentMethod || 'ach_standard';

    // Step 2: Validation (2-5 seconds)
    await new Promise(resolve => setTimeout(resolve, 2000 + Math.random() * 3000));
    await progressWithdrawalToNextStep(transactionId, 'validated', 'completed', 'Withdrawal request validated successfully');

    // Step 3: Processing based on payment method
    if (paymentMethod === 'wire_transfer') {
      // Wire transfers need manual review
      await new Promise(resolve => setTimeout(resolve, 3000));
      await progressWithdrawalToNextStep(transactionId, 'under_review', 'completed', 'Wire transfer under manual review for security');

      // Simulate review time (10-30 seconds for demo, would be hours in real life)
      await new Promise(resolve => setTimeout(resolve, 10000 + Math.random() * 20000));
      await progressWithdrawalToNextStep(transactionId, 'review_approved', 'completed', 'Manual review completed - withdrawal approved');
    }

    // Step 4: Processing
    await new Promise(resolve => setTimeout(resolve, 3000 + Math.random() * 5000));
    await progressWithdrawalToNextStep(transactionId, 'processing', 'completed', 'Payment processing initiated with bank');

    // Step 5: Sent to bank
    await new Promise(resolve => setTimeout(resolve, 5000 + Math.random() * 10000));
    await progressWithdrawalToNextStep(transactionId, 'sent_to_bank', 'completed', 'Payment instruction sent to your bank');

    // Step 6: Bank processing (simulate bank response time)
    const processingTime = paymentMethod === 'ach_same_day' ? 15000 : 30000; // Faster for same-day ACH
    await new Promise(resolve => setTimeout(resolve, processingTime + Math.random() * 15000));

    // Simulate 95% success rate
    const isSuccessful = Math.random() > 0.05;

    if (isSuccessful) {
      // Step 7: Transferred
      await progressWithdrawalToNextStep(transactionId, 'transferred', 'completed', 'Funds successfully transferred to your bank account');

      // Step 8: Completed
      await new Promise(resolve => setTimeout(resolve, 2000));
      await progressWithdrawalToNextStep(transactionId, 'completed', 'completed', 'Withdrawal completed successfully');

      // Update transaction status to completed
      await executeUpdate(
        'UPDATE tbl_wallet_transactions SET status_id = ? WHERE id = ?',
        [1, transactionId] // 1 = completed status
      );

      logger.info('Withdrawal processing completed successfully', { transactionId });
    } else {
      // Simulate bank rejection
      await progressWithdrawalToNextStep(transactionId, 'rejected_by_bank', 'failed', 'Bank rejected the withdrawal - insufficient funds or account issues');

      // Reverse the transaction
      await reverseWithdrawal(transactionId, 'Bank rejection - funds returned to wallet');

      logger.warn('Withdrawal rejected by bank', { transactionId });
    }

  } catch (error) {
    logger.error('Error in withdrawal processing flow', { error, transactionId });

    // Mark as failed and reverse
    try {
      await progressWithdrawalToNextStep(transactionId, 'failed_processing', 'failed', 'Processing failed due to system error');
      await reverseWithdrawal(transactionId, 'System error - funds returned to wallet');
    } catch (reverseError) {
      logger.error('Error reversing failed withdrawal', { reverseError, transactionId });
    }
  }
}

/**
 * Reverse a withdrawal and return funds to wallet
 */
async function reverseWithdrawal(transactionId: number, reason: string): Promise<void> {
  try {
    // Get original transaction
    const transaction = await executeQuerySingle(
      'SELECT * FROM tbl_wallet_transactions WHERE id = ?',
      [transactionId]
    );

    if (!transaction) {
      throw new Error('Transaction not found for reversal');
    }

    const userId = transaction.user_id;
    const reversalAmount = Math.abs(parseFloat(transaction.amount.toString()));

    // Get current wallet balance
    const wallet = await executeQuerySingle(
      'SELECT balance FROM tbl_masterwallet WHERE user_id = ?',
      [userId]
    );

    if (!wallet) {
      throw new Error('Wallet not found for reversal');
    }

    const currentBalance = parseFloat(wallet.balance.toString());
    const newBalance = currentBalance + reversalAmount;

    // Update wallet balance
    await executeUpdate(
      'UPDATE tbl_masterwallet SET balance = ?, last_updated = NOW() WHERE user_id = ?',
      [newBalance, userId]
    );

    // Create reversal transaction
    const { createWalletTransaction } = await import('./walletService');
    await createWalletTransaction(
      userId,
      reversalAmount,
      `Withdrawal reversal - ${reason}`,
      'reversal',
      `REVERSAL_${transactionId}_${Date.now()}`,
      {
        originalTransactionId: transactionId,
        reversalReason: reason,
        status: 'completed'
      }
    );

    // Update original transaction status
    await executeUpdate(
      'UPDATE tbl_wallet_transactions SET status_id = ? WHERE id = ?',
      [3, transactionId] // Assuming 3 is failed/reversed status
    );

    // Add reversal step
    await progressWithdrawalToNextStep(transactionId, 'reversed', 'completed', reason);

    logger.info('Withdrawal reversed successfully', { transactionId, reversalAmount, newBalance });

  } catch (error) {
    logger.error('Error reversing withdrawal', { error, transactionId });
    throw error;
  }
}

/**
 * Handle successful withdrawal flow (step by step)
 */
export async function processSuccessfulWithdrawal(transactionId: number, paymentMethod: string): Promise<boolean> {
  try {
    // Add steps one by one as they would happen in real flow
    await progressWithdrawalToNextStep(transactionId, 'validated', 'completed', 'Withdrawal request validated successfully');

    if (paymentMethod === 'wire_transfer') {
      await progressWithdrawalToNextStep(transactionId, 'under_review', 'completed', 'Wire transfer under manual review');
      await progressWithdrawalToNextStep(transactionId, 'review_approved', 'completed', 'Manual review completed - approved');
    }

    await progressWithdrawalToNextStep(transactionId, 'processing', 'completed', 'Payment processing initiated');
    await progressWithdrawalToNextStep(transactionId, 'sent_to_bank', 'completed', 'Payment instruction sent to bank');
    await progressWithdrawalToNextStep(transactionId, 'transferred', 'completed', 'Funds transferred successfully');
    await progressWithdrawalToNextStep(transactionId, 'completed', 'completed', 'Withdrawal completed successfully');

    // Update main transaction status to completed
    await executeUpdate(
      'UPDATE tbl_wallet_transactions SET status_id = ? WHERE id = ?',
      [1, transactionId] // 1 = completed status
    );

    logger.info('Successful withdrawal flow processed', { transactionId, paymentMethod });
    return true;
  } catch (error) {
    logger.error('Error processing successful withdrawal', { error, transactionId });
    return false;
  }
}

/**
 * Handle failed withdrawal flow (step by step)
 */
export async function processFailedWithdrawal(
  transactionId: number,
  failurePoint: 'validation' | 'processing' | 'bank_rejection',
  reason: string
): Promise<boolean> {
  try {
    switch (failurePoint) {
      case 'validation':
        await progressWithdrawalToNextStep(transactionId, 'failed_validation', 'failed', reason);
        break;
      case 'processing':
        await progressWithdrawalToNextStep(transactionId, 'validated', 'completed', 'Withdrawal request validated');
        await progressWithdrawalToNextStep(transactionId, 'failed_processing', 'failed', reason);
        break;
      case 'bank_rejection':
        await progressWithdrawalToNextStep(transactionId, 'validated', 'completed', 'Withdrawal request validated');
        await progressWithdrawalToNextStep(transactionId, 'processing', 'completed', 'Payment processing initiated');
        await progressWithdrawalToNextStep(transactionId, 'sent_to_bank', 'completed', 'Payment instruction sent to bank');
        await progressWithdrawalToNextStep(transactionId, 'rejected_by_bank', 'failed', reason);
        break;
    }

    logger.info('Failed withdrawal flow processed', { transactionId, failurePoint, reason });
    return true;
  } catch (error) {
    logger.error('Error processing failed withdrawal', { error, transactionId, failurePoint });
    return false;
  }
}

/**
 * Handle withdrawal reversal/refund
 */
export async function processWithdrawalReversal(
  transactionId: number,
  reversalType: 'reversed' | 'refunded',
  reason: string
): Promise<boolean> {
  try {
    // Add the reversal step
    await addWithdrawalStep(transactionId, reversalType, 'completed', reason);

    logger.info('Withdrawal reversal processed', { transactionId, reversalType, reason });
    return true;
  } catch (error) {
    logger.error('Error processing withdrawal reversal', { error, transactionId, reversalType });
    return false;
  }
}

/**
 * Handle withdrawal cancellation
 */
export async function processWithdrawalCancellation(transactionId: number, reason: string): Promise<boolean> {
  try {
    await addWithdrawalStep(transactionId, 'cancelled', 'completed', reason);

    logger.info('Withdrawal cancellation processed', { transactionId, reason });
    return true;
  } catch (error) {
    logger.error('Error processing withdrawal cancellation', { error, transactionId });
    return false;
  }
}

/**
 * Handle withdrawal under review
 */
export async function processWithdrawalReview(
  transactionId: number,
  reviewResult: 'approved' | 'rejected',
  reason: string
): Promise<boolean> {
  try {
    await addWithdrawalStep(transactionId, 'validated', 'completed', 'Withdrawal request validated');
    await addWithdrawalStep(transactionId, 'under_review', 'completed', 'Withdrawal flagged for manual review');

    if (reviewResult === 'approved') {
      await addWithdrawalStep(transactionId, 'review_approved', 'completed', reason);
      // Continue with normal processing
      await addWithdrawalStep(transactionId, 'processing', 'completed', 'Payment processing initiated after review');
    } else {
      await addWithdrawalStep(transactionId, 'review_rejected', 'failed', reason);
    }

    logger.info('Withdrawal review processed', { transactionId, reviewResult, reason });
    return true;
  } catch (error) {
    logger.error('Error processing withdrawal review', { error, transactionId, reviewResult });
    return false;
  }
}
