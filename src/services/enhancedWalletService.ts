import { executeQuerySingle, executeQuery } from '../utils/database';
import { getAvailableWalletBalance, getUserPendingHolds } from './pendingHoldService';
import logger from '../utils/logger';

export interface WalletInfo {
  actualBalance: number;
  availableBalance: number;
  pendingWithdrawals: number;
  pendingDeposits: number;
  pendingHolds: any[];
}

/**
 * Get comprehensive wallet information including pending holds
 */
export async function getWalletInfo(userId: number): Promise<WalletInfo | null> {
  try {
    // Import the original wallet service
    const { getUserMasterWallet } = await import('./walletService');
    
    // Get actual wallet balance
    const wallet = await getUserMasterWallet(userId);
    if (!wallet) {
      return null;
    }

    const actualBalance = parseFloat(wallet.balance.toString());

    // Get available balance (minus pending withdrawal holds)
    const availableBalance = await getAvailableWalletBalance(userId);

    // Get pending holds
    const pendingHolds = await getUserPendingHolds(userId);

    // Calculate pending amounts
    let pendingWithdrawals = 0;
    let pendingDeposits = 0;

    for (const hold of pendingHolds) {
      if (hold.type === 'withdrawal') {
        pendingWithdrawals += parseFloat(hold.amount);
      } else if (hold.type === 'deposit') {
        pendingDeposits += parseFloat(hold.amount);
      }
    }

    logger.info('Wallet info calculated', {
      userId,
      actualBalance,
      availableBalance,
      pendingWithdrawals,
      pendingDeposits,
      totalHolds: pendingHolds.length
    });

    return {
      actualBalance,
      availableBalance,
      pendingWithdrawals,
      pendingDeposits,
      pendingHolds
    };

  } catch (error) {
    logger.error('Error getting wallet info', { userId, error });
    return null;
  }
}

/**
 * Check if user can make a withdrawal of specified amount
 */
export async function canWithdraw(userId: number, amount: number): Promise<{
  canWithdraw: boolean;
  availableBalance: number;
  message?: string;
}> {
  try {
    const availableBalance = await getAvailableWalletBalance(userId);
    const canWithdraw = availableBalance >= amount;

    return {
      canWithdraw,
      availableBalance,
      message: canWithdraw 
        ? 'Sufficient balance available' 
        : `Insufficient balance. Available: $${availableBalance.toFixed(2)}, Required: $${amount.toFixed(2)}`
    };

  } catch (error) {
    logger.error('Error checking withdrawal capability', { userId, amount, error });
    return {
      canWithdraw: false,
      availableBalance: 0,
      message: 'Error checking balance'
    };
  }
}

/**
 * Get wallet transaction history with pending hold information
 */
export async function getWalletTransactionHistory(
  userId: number,
  limit: number = 20,
  offset: number = 0
): Promise<{
  transactions: any[];
  pendingHolds: any[];
  summary: {
    actualBalance: number;
    availableBalance: number;
    pendingWithdrawals: number;
    pendingDeposits: number;
  };
}> {
  try {
    // Get transactions
    const transactions = await executeQuery(
      `SELECT id, type, amount, reference_id, payment_provider, description, 
              status_id, created_at, meta_data
       FROM tbl_wallet_transactions 
       WHERE user_id = ? 
       ORDER BY created_at DESC 
       LIMIT ? OFFSET ?`,
      [userId, limit, offset]
    );

    // Get wallet info
    const walletInfo = await getWalletInfo(userId);

    return {
      transactions: Array.isArray(transactions) ? transactions : (transactions ? [transactions] : []),
      pendingHolds: walletInfo?.pendingHolds || [],
      summary: {
        actualBalance: walletInfo?.actualBalance || 0,
        availableBalance: walletInfo?.availableBalance || 0,
        pendingWithdrawals: walletInfo?.pendingWithdrawals || 0,
        pendingDeposits: walletInfo?.pendingDeposits || 0
      }
    };

  } catch (error) {
    logger.error('Error getting wallet transaction history', { userId, error });
    return {
      transactions: [],
      pendingHolds: [],
      summary: {
        actualBalance: 0,
        availableBalance: 0,
        pendingWithdrawals: 0,
        pendingDeposits: 0
      }
    };
  }
}