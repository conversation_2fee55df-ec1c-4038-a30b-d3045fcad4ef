import { executeQuery, executeQ<PERSON><PERSON><PERSON>ing<PERSON> } from '../utils/database';
import logger from '../utils/logger';

export interface WalletBalance {
  id: number;
  user_id: number;
  balance: number;
  pending_balance: number;
  blocked_balance: number;
  available_balance: number;
  created_at: Date;
  last_updated: Date;
}

export interface HoldResult {
  hold_id?: number;
  status: string;
  message: string;
}

export interface ConfirmResult {
  status: string;
  message: string;
  user_id?: number;
  amount?: number;
}

/**
 * Get enhanced wallet balance using stored procedure
 */
export async function getEnhancedWalletBalanceSP(userId: number): Promise<WalletBalance | null> {
  try {
    const result = await executeQuerySingle(
      'CALL sp_get_enhanced_wallet_balance(?)',
      [userId]
    );

    if (!result) {
      return null;
    }

    return {
      id: result.id,
      user_id: result.user_id,
      balance: parseFloat(result.balance.toString()),
      pending_balance: parseFloat(result.pending_balance.toString()),
      blocked_balance: parseFloat(result.blocked_balance.toString()),
      available_balance: parseFloat(result.available_balance.toString()),
      created_at: result.created_at,
      last_updated: result.last_updated
    };
  } catch (error) {
    logger.error('Error getting enhanced wallet balance via SP', { userId, error });
    return null;
  }
}

/**
 * Update wallet balance safely using stored procedure
 */
export async function updateWalletBalanceSP(
  userId: number,
  balanceChange: number,
  pendingChange: number,
  blockedChange: number,
  description: string,
  referenceId?: string
): Promise<{ success: boolean; message?: string }> {
  try {
    await executeQuery(
      'CALL sp_update_wallet_balance(?, ?, ?, ?, ?, ?)',
      [userId, balanceChange, pendingChange, blockedChange, description, referenceId || null]
    );

    logger.info('Wallet balance updated via SP', {
      userId,
      balanceChange,
      pendingChange,
      blockedChange,
      description,
      referenceId
    });

    return { success: true };
  } catch (error) {
    logger.error('Error updating wallet balance via SP', {
      userId,
      balanceChange,
      pendingChange,
      blockedChange,
      error: error instanceof Error ? error.message : 'Unknown error'
    });
    return {
      success: false,
      message: error instanceof Error ? error.message : 'Failed to update wallet balance'
    };
  }
}

/**
 * Create pending deposit using stored procedure
 */
export async function createPendingDepositSP(
  userId: number,
  amount: number,
  plaidTransferId: string,
  description: string,
  metadata: any = {}
): Promise<HoldResult> {
  try {
    const result = await executeQuerySingle(
      'CALL sp_create_pending_hold(?, ?, ?, ?, ?, ?)',
      [
        userId,
        amount,
        'deposit',
        plaidTransferId,
        description,
        JSON.stringify(metadata)
      ]
    );

    logger.info('Pending deposit created via SP', {
      userId,
      amount,
      plaidTransferId,
      holdId: result?.hold_id
    });

    return {
      hold_id: result?.hold_id,
      status: result?.status || 'success',
      message: result?.message || 'Pending deposit created successfully'
    };
  } catch (error) {
    logger.error('Error creating pending deposit via SP', { userId, amount, plaidTransferId, error });
    return {
      status: 'error',
      message: error instanceof Error ? error.message : 'Failed to create pending deposit'
    };
  }
}

/**
 * Create withdrawal hold using stored procedure
 */
export async function createWithdrawalHoldSP(
  userId: number,
  amount: number,
  plaidTransferId: string,
  description: string,
  metadata: any = {}
): Promise<HoldResult> {
  try {
    // First check if user has sufficient available balance
    const balance = await getEnhancedWalletBalanceSP(userId);
    if (!balance || balance.available_balance < amount) {
      return {
        status: 'error',
        message: 'Insufficient available balance'
      };
    }

    const result = await executeQuerySingle(
      'CALL sp_create_pending_hold(?, ?, ?, ?, ?, ?)',
      [
        userId,
        amount,
        'withdrawal',
        plaidTransferId,
        description,
        JSON.stringify(metadata)
      ]
    );

    logger.info('Withdrawal hold created via SP', {
      userId,
      amount,
      plaidTransferId,
      holdId: result?.hold_id
    });

    return {
      hold_id: result?.hold_id,
      status: result?.status || 'success',
      message: result?.message || 'Withdrawal hold created successfully'
    };
  } catch (error) {
    logger.error('Error creating withdrawal hold via SP', { userId, amount, plaidTransferId, error });
    return {
      status: 'error',
      message: error instanceof Error ? error.message : 'Failed to create withdrawal hold'
    };
  }
}

/**
 * Confirm pending hold using stored procedure
 */
export async function confirmPendingHoldSP(
  plaidTransferId: string,
  plaidStatus: string = 'posted'
): Promise<ConfirmResult> {
  try {
    const result = await executeQuerySingle(
      'CALL sp_confirm_pending_hold(?, ?)',
      [plaidTransferId, plaidStatus]
    );

    if (result?.status === 'not_found') {
      return {
        status: 'not_found',
        message: 'Pending hold not found'
      };
    }

    logger.info('Pending hold confirmed via SP', {
      plaidTransferId,
      plaidStatus,
      userId: result?.user_id,
      amount: result?.amount
    });

    return {
      status: result?.status || 'success',
      message: result?.message || 'Hold confirmed successfully',
      user_id: result?.user_id,
      amount: result?.amount ? parseFloat(result.amount.toString()) : undefined
    };
  } catch (error) {
    logger.error('Error confirming pending hold via SP', { plaidTransferId, plaidStatus, error });
    return {
      status: 'error',
      message: error instanceof Error ? error.message : 'Failed to confirm pending hold'
    };
  }
}

/**
 * Release failed hold using stored procedure approach
 */
export async function releaseFailedHoldSP(
  plaidTransferId: string,
  failureReason: string = 'Transfer failed'
): Promise<ConfirmResult> {
  try {
    // Find the pending hold
    const hold = await executeQuerySingle(
      `SELECT * FROM tbl_pending_holds 
       WHERE plaid_transfer_id = ? AND status = 'pending'`,
      [plaidTransferId]
    );

    if (!hold) {
      return {
        status: 'not_found',
        message: 'Pending hold not found'
      };
    }

    const amount = parseFloat(hold.amount.toString());
    const userId = hold.user_id;
    const holdType = hold.type;

    // Update the hold status
    await executeQuery(
      `UPDATE tbl_pending_holds 
       SET status = 'failed', updated_at = NOW()
       WHERE id = ?`,
      [hold.id]
    );

    // Reverse the hold based on type using stored procedure
    if (holdType === 'deposit') {
      // Remove from pending balance
      await updateWalletBalanceSP(
        userId,
        0,
        -amount,
        0,
        `Failed deposit released: ${failureReason}`,
        plaidTransferId
      );
    } else if (holdType === 'withdrawal') {
      // Remove from blocked balance (restore available balance)
      await updateWalletBalanceSP(
        userId,
        0,
        0,
        -amount,
        `Failed withdrawal released: ${failureReason}`,
        plaidTransferId
      );
    }

    // Update the wallet transaction status
    await executeQuery(
      `UPDATE tbl_wallet_transactions 
       SET status_id = 'failed', plaid_status = 'failed', failure_reason = ?, plaid_status_updated_at = NOW()
       WHERE reference_id = ? OR authorization_id = ?`,
      [failureReason, plaidTransferId, plaidTransferId]
    );

    logger.info('Failed hold released via SP', {
      userId,
      amount,
      holdType,
      plaidTransferId,
      holdId: hold.id,
      failureReason
    });

    return {
      status: 'success',
      message: 'Failed hold released successfully',
      user_id: userId,
      amount
    };

  } catch (error) {
    logger.error('Error releasing failed hold via SP', { plaidTransferId, failureReason, error });
    return {
      status: 'error',
      message: error instanceof Error ? error.message : 'Failed to release hold'
    };
  }
}

/**
 * Get wallet balance summary with formatted values
 */
export async function getWalletBalanceSummarySP(userId: number): Promise<{
  balance: WalletBalance;
  formatted: {
    total: string;
    available: string;
    pending: string;
    blocked: string;
  };
  status: {
    hasPendingTransactions: boolean;
    canSpend: boolean;
    totalPendingAmount: number;
    totalBlockedAmount: number;
  };
} | null> {
  try {
    const balance = await getEnhancedWalletBalanceSP(userId);
    if (!balance) {
      return null;
    }

    return {
      balance,
      formatted: {
        total: `$${balance.balance.toFixed(2)}`,
        available: `$${balance.available_balance.toFixed(2)}`,
        pending: `$${balance.pending_balance.toFixed(2)}`,
        blocked: `$${balance.blocked_balance.toFixed(2)}`
      },
      status: {
        hasPendingTransactions: balance.pending_balance > 0 || balance.blocked_balance > 0,
        canSpend: balance.available_balance > 0,
        totalPendingAmount: balance.pending_balance,
        totalBlockedAmount: balance.blocked_balance
      }
    };
  } catch (error) {
    logger.error('Error getting wallet balance summary via SP', { userId, error });
    return null;
  }
}
