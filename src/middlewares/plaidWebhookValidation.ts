import { Request, Response, NextFunction } from 'express';
import { plaidClient } from '../config/plaidConfig';
import { logWebhookEvent } from '../services/auditService';
import logger from '../utils/logger';

/**
 * Middleware to validate Plaid webhook signatures
 * Based on Plaid's documentation: https://plaid.com/docs/api/webhooks/webhook-verification/
 */
export const validatePlaidWebhook = async (req: Request, res: Response, next: NextFunction) => {
  try {
    const webhookSecret = process.env.PLAID_WEBHOOK_SECRET;
    
    // If webhook secret is not configured, log a warning and continue
    if (!webhookSecret) {
      logger.warn('PLAID_WEBHOOK_SECRET not configured, skipping webhook signature verification');
      return next();
    }
    
    const plaidWebhookSignature = req.headers['plaid-verification'] as string;
    
    // If no signature is provided, reject the webhook
    if (!plaidWebhookSignature) {
      logger.warn('Missing Plaid webhook signature', { 
        ip: req.ip, 
        headers: req.headers,
        path: req.path
      });
      
      // Log security event
      await logWebhookEvent(
        'SECURITY',
        'SIGNATURE_MISSING',
        { 
          headers: req.headers,
          ip: req.ip,
          path: req.path
        },
        { 
          verified: false,
          reason: 'Missing Plaid-Verification header'
        },
        false
      );
      
      return res.status(401).json({ error: 'Invalid webhook signature' });
    }
    
    // Get the raw body for verification
    const rawBody = req.body;
    
    try {
      // Verify the webhook signature using Plaid's API
      const response = await plaidClient.webhookVerificationKeyGet({});
      const key = response.data.key;
      
      // Verify the webhook signature
      const isValid = await verifyWebhookSignature(
        JSON.stringify(rawBody),
        plaidWebhookSignature,
        webhookSecret,
        key
      );
      
      if (!isValid) {
        logger.warn('Invalid Plaid webhook signature', { 
          ip: req.ip,
          path: req.path
        });
        
        // Log security event
        await logWebhookEvent(
          'SECURITY',
          'SIGNATURE_INVALID',
          { 
            headers: req.headers,
            ip: req.ip,
            path: req.path
          },
          { 
            verified: false,
            reason: 'Invalid signature'
          },
          false
        );
        
        return res.status(401).json({ error: 'Invalid webhook signature' });
      }
      
      // Log successful verification
      logger.info('Plaid webhook signature verified', {
        webhook_type: rawBody.webhook_type,
        webhook_code: rawBody.webhook_code
      });
      
      // Set a header to indicate the webhook was verified
      req.headers['x-webhook-verified'] = 'true';
      
      // Continue to the next middleware/controller
      next();
      
    } catch (error) {
      logger.error('Error verifying Plaid webhook signature', { error });
      
      // Log security event
      await logWebhookEvent(
        'SECURITY',
        'SIGNATURE_VERIFICATION_ERROR',
        { 
          headers: req.headers,
          ip: req.ip,
          path: req.path,
          error: error.message
        },
        { 
          verified: false,
          reason: 'Verification error'
        },
        false
      );
      
      // In case of verification error, reject the webhook
      return res.status(500).json({ error: 'Error verifying webhook signature' });
    }
    
  } catch (error) {
    logger.error('Unexpected error in Plaid webhook validation middleware', { error });
    
    // Log security event
    await logWebhookEvent(
      'SECURITY',
      'MIDDLEWARE_ERROR',
      { 
        headers: req.headers,
        ip: req.ip,
        path: req.path,
        error: error.message
      },
      { 
        verified: false,
        reason: 'Middleware error'
      },
      false
    );
    
    return res.status(500).json({ error: 'Internal server error' });
  }
};

/**
 * Verify the webhook signature using the provided key
 * @param body - The raw request body as a string
 * @param signature - The Plaid-Verification header value
 * @param secret - The webhook secret
 * @param key - The verification key from Plaid
 * @returns boolean - Whether the signature is valid
 */
async function verifyWebhookSignature(
  body: string,
  signature: string,
  secret: string,
  key: string
): Promise<boolean> {
  try {
    // In a production environment, you would use a crypto library to verify the signature
    // For example, using the 'crypto' module in Node.js
    
    // This is a placeholder implementation
    // In a real implementation, you would:
    // 1. Parse the signature header to get the timestamp and signature
    // 2. Verify the timestamp is recent (to prevent replay attacks)
    // 3. Compute the expected signature using the body, timestamp, and secret
    // 4. Compare the computed signature with the provided signature
    
    // For now, we'll return true in development/sandbox environments
    // and implement proper verification in production
    if (process.env.NODE_ENV === 'production') {
      // TODO: Implement actual signature verification for production
      // This would use the key provided by Plaid to verify the signature
      
      // Example implementation (pseudocode):
      // const crypto = require('crypto');
      // const [timestamp, providedSignature] = signature.split(',');
      // const expectedSignature = crypto
      //   .createHmac('sha256', secret)
      //   .update(timestamp + body)
      //   .digest('hex');
      // return crypto.timingSafeEqual(
      //   Buffer.from(providedSignature),
      //   Buffer.from(expectedSignature)
      // );
      
      // For now, return true to allow development
      return true;
    }
    
    // In development/sandbox, accept all webhooks
    return true;
  } catch (error) {
    logger.error('Error in webhook signature verification', { error });
    return false;
  }
}