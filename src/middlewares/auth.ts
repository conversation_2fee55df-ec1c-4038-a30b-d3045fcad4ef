import { Request, Response, NextFunction } from 'express';
import jwt from 'jsonwebtoken';
import dotenv from 'dotenv';

dotenv.config();

// In-memory blacklist for revoked tokens (replace with a database or cache in production)
const tokenBlacklist: Set<string> = new Set();

interface UserPayload {
  id: string;
}

declare global {
  namespace Express {
    interface Request {
      userId?: string;
      master_parent_user_id?: string;
      team_connect_user_id?: string;
    }
  }
}

export const authenticateToken = (req: Request, res: Response, next: NextFunction): void => {
  const authHeader = req.headers['authorization'];
  const token = authHeader && authHeader.split(' ')[1];

  if (token == null) {
    res.status(401).json({ message: 'Unauthorized' });
    return;
  }

  if (tokenBlacklist.has(token)) {
    res.status(401).json({ message: 'Token revoked' });
    return;
  }


  const jwtSecret = process.env.JWT_SECRET;
  if (!jwtSecret) {
    res.status(500).json({ message: 'JWT secret not configured' });
    return;
  }

  jwt.verify(token, jwtSecret, (err: any, user: any) => {
    if (err) {
      console.log(err, "error", token, jwtSecret);
      res.status(403).json({ message: 'Invalid token' });
      return;
    }
    if (typeof user === 'object' && user !== null && 'id' in user) {
      req.userId = (user as { id: string }).id;
      if ('team_connect_user_id' in user) {
        req.team_connect_user_id = (user as { team_connect_user_id: string }).team_connect_user_id;
      }
      if ('master_parent_user_id' in user) {
        req.master_parent_user_id = (user as { master_parent_user_id: string }).master_parent_user_id;
      }
      next();
    } else {
      console.log(err, "error");
      res.status(403).json({ message: 'Invalid token' });
      return;
    }
  });
};
