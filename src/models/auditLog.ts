import { executeQuery, executeQuery<PERSON>ingle, executeUpdate } from '../utils/database';
import logger from '../utils/logger';

export interface AuditLog {
  id: number;
  user_id: number;
  action: string;
  entity_type: string;
  entity_id: string;
  details: Record<string, any>;
  created_at: Date;
}

/**
 * Create an audit log entry
 * @param userId - User ID who performed the action
 * @param action - Action performed (e.g., 'add_bank_account', 'replace_bank_account')
 * @param entityType - Type of entity affected (e.g., 'staff_bank_account')
 * @param entityId - ID of the entity affected
 * @param details - Additional details about the action
 * @returns Promise<number | null> - ID of the created audit log or null if failed
 */
export async function createAuditLog(
  userId: number,
  action: string,
  entityType: string,
  entityId: string,
  details: Record<string, any>
): Promise<number | null> {
  try {
    // Ensure sensitive data is masked
    const sanitizedDetails = maskSensitiveData(details);
    
    const result = await executeUpdate(
      `INSERT INTO tbl_audit_logs
       (user_id, action_type, reference_table, reference_id, details, created_at)
       VALUES (?, ?, ?, ?, ?, NOW())`,
      [
        userId,
        action,
        entityType,
        parseInt(entityId),
        JSON.stringify(sanitizedDetails)
      ]
    );

    return result && result.insertId ? result.insertId : null;
  } catch (error) {
    logger.error('Error creating audit log', { userId, action, entityType, entityId, error });
    return null;
  }
}

/**
 * Get audit logs for a specific entity
 * @param entityType - Type of entity
 * @param entityId - ID of the entity
 * @param limit - Maximum number of logs to return
 * @param offset - Offset for pagination
 * @returns Promise<AuditLog[]> - Array of audit logs
 */
export async function getAuditLogs(
  entityType: string,
  entityId: string,
  limit: number = 50,
  offset: number = 0
): Promise<AuditLog[]> {
  try {
    const logs = await executeQuery<AuditLog>(
      `SELECT * FROM tbl_audit_logs
       WHERE reference_table = ? AND reference_id = ?
       ORDER BY created_at DESC
       LIMIT ? OFFSET ?`,
      [entityType, parseInt(entityId), limit, offset]
    );

    return Array.isArray(logs) ? logs : (logs ? [logs] : []);
  } catch (error) {
    logger.error('Error getting audit logs', { entityType, entityId, error });
    return [];
  }
}

/**
 * Mask sensitive data in audit logs
 * @param details - Object containing details that may include sensitive data
 * @returns Record<string, any> - Object with sensitive data masked
 */
export function maskSensitiveData(details: Record<string, any>): Record<string, any> {
  const sensitiveFields = [
    'accountNumber', 'account_number',
    'routingNumber', 'routing_number',
    'ssn', 'socialSecurityNumber', 'social_security_number',
    'password', 'pin', 'secret'
  ];
  
  const maskedDetails = { ...details };
  
  for (const key of Object.keys(maskedDetails)) {
    if (sensitiveFields.includes(key) && typeof maskedDetails[key] === 'string') {
      const value = maskedDetails[key];
      if (value.length > 4) {
        maskedDetails[key] = `****${value.slice(-4)}`;
      } else {
        maskedDetails[key] = '****';
      }
    } else if (typeof maskedDetails[key] === 'object' && maskedDetails[key] !== null) {
      maskedDetails[key] = maskSensitiveData(maskedDetails[key]);
    }
  }
  
  return maskedDetails;
}